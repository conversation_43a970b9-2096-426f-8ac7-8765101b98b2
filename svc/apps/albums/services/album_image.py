from typing import Optional
from redis.asyncio import Redis
from fastapi_events.dispatcher import dispatch

from svc.apps.albums.models.album_image import AlbumImage
from svc.apps.albums.repositories.album_image import AlbumImageRepository
from svc.apps.albums.schemas.album_image import (
    AlbumImageCreate, AlbumImageUpdate, AlbumImageResponse, AlbumImageListResponse, GetAlbumImagesParams
)
from svc.core.services.base import BaseService
from svc.core.services.error_result_mixin import ErrorResultMixin
from svc.core.services.batch_operation_mixin import BatchOperationMixin
from svc.core.services.cache_mixin import CacheMixin
from svc.core.models.result import Result
from svc.core.exceptions.error_codes import ErrorCode

CACHE_TTL = 1800

class AlbumImageService(BaseService, ErrorResultMixin, BatchOperationMixin, CacheMixin):
    """图册图片服务类，提供图片的创建、查询和管理功能"""
    resource_type = "album_image"

    def __init__(self, redis: Optional[Redis] = None, image_repo: Optional[AlbumImageRepository] = None):
        BaseService.__init__(self, redis)
        self.image_repo = image_repo

    async def get_resource_by_id(self, image_id: int) -> Optional[AlbumImage]:
        return await self.image_repo.get_by_id(image_id)

    async def get_album_image(self, image_id: int) -> Result[AlbumImageResponse]:
        try:
            image = await self.get_resource_by_id(image_id)
            if not image:
                return self.resource_not_found_result(image_id)
            response = AlbumImageResponse.model_validate(image.to_dict())
            return self.create_success_result(response)
        except Exception as e:
            return self.create_error_result(
                error_code=ErrorCode.NOT_FOUND,
                error_message=f"获取图片失败: {str(e)}"
            )

    async def get_album_images(self, params: GetAlbumImagesParams) -> Result[AlbumImageListResponse]:
        try:
            page_num = params.page_num
            page_size = params.page_size
            skip = (page_num - 1) * page_size
            images, total = await self.image_repo.get_album_images(
                skip=skip,
                limit=page_size,
                album_id=params.album_id,
                status=params.status,
                search_term=params.search_term,
                order_by=params.order_by,
                order_desc=params.order_desc
            )
            responses = [AlbumImageResponse.model_validate(i.to_dict()) for i in images]
            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0
            paginated = AlbumImageListResponse(
                items=responses,
                total=total,
                page_num=page_num,
                page_size=page_size,
                page_count=total_pages
            )
            return self.create_success_result(paginated)
        except Exception as e:
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取图片列表失败: {str(e)}"
            )

    async def create_album_image(self, params: AlbumImageCreate) -> Result[AlbumImageResponse]:
        try:
            image = await self.image_repo.create(**params.model_dump())
            response = AlbumImageResponse.model_validate(image.to_dict())
            dispatch("album_image:uploaded", payload=response.model_dump())
            return self.create_success_result(response)
        except Exception as e:
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"上传图片失败: {str(e)}"
            )

    async def update_album_image(self, image_id: int, params: AlbumImageUpdate) -> Result[AlbumImageResponse]:
        try:
            image = await self.get_resource_by_id(image_id)
            if not image:
                return self.resource_not_found_result(image_id)
            update_data = params.model_dump(exclude_unset=True)
            image = await self.image_repo.update(image, data=update_data)
            response = AlbumImageResponse.model_validate(image.to_dict())
            dispatch("album_image:updated", payload=response.model_dump())
            return self.create_success_result(response)
        except Exception as e:
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"更新图片失败: {str(e)}"
            )

    async def delete_album_image(self, image_id: int) -> Result:
        try:
            image = await self.get_resource_by_id(image_id)
            if not image:
                return self.resource_not_found_result(image_id)
            await self.image_repo.delete(image)
            dispatch("album_image:deleted", payload={"id": image_id})
            return self.create_success_result({"id": image_id})
        except Exception as e:
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"删除图片失败: {str(e)}"
            ) 