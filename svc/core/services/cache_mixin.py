import json
from typing import Any, Callable, Dict, List, Optional
from redis.asyncio import Redis

CACHE_VERSION = "v1"
CACHE_TTL = 3600
CACHE_TTL_SHORT = 300
CACHE_TTL_LONG = 86400

class CacheMixin(object):
    """缓存相关功能mixin，需注入self.redis"""
    redis: Optional[Redis] = None
    resource_type: str = "资源"

    def _get_resource_cache_key(self, resource_id: int) -> str:
        return f"{self.resource_type}:{CACHE_VERSION}:{resource_id}"

    def _get_collection_cache_key(self, collection_name: str, **filters) -> str:
        filter_str = ":".join(f"{k}={v}" for k, v in sorted(filters.items()))
        return f"{self.resource_type}:{CACHE_VERSION}:collection:{collection_name}:{filter_str}"

    def _get_cache_stats_key(self) -> str:
        return f"{self.resource_type}:{CACHE_VERSION}:stats"

    async def _increment_cache_stats(self, stat_type: str) -> None:
        if not self.redis:
            return
        try:
            stats_key = self._get_cache_stats_key()
            try:
                await self.redis.hincrby(stats_key, stat_type, 1)
            except Exception as e:
                print(f"增加缓存统计时出错: {str(e)}")
        except Exception as e:
            print(f"更新缓存统计失败: stat_type={stat_type}, 错误={str(e)}")

    async def cache_resource(self, key: str, resource: Any, expire: int = CACHE_TTL) -> None:
        if not self.redis:
            return
        try:
            if hasattr(resource, 'to_dict'):
                data = resource.to_dict()
            elif hasattr(resource, 'model_dump'):
                data = resource.model_dump()
            else:
                data = resource
            try:
                await self.redis.set(key, json.dumps(data, ensure_ascii=False), ex=expire)
            except Exception as e:
                print(f"缓存资源时出错: key={key}, 错误={str(e)}")
            print(f"资源缓存成功: {key}")
        except Exception as e:
            await self._increment_cache_stats("error")
            print(f"缓存资源失败: key={key}, 错误={str(e)}")

    async def get_cached_resource(self, key: str, deserializer: Callable[[Dict[str, Any]], Any]) -> Optional[Any]:
        if not self.redis:
            return None
        try:
            try:
                data = await self.redis.get(key)
                if data:
                    await self._increment_cache_stats("hit")
                    return deserializer(json.loads(data))
                else:
                    await self._increment_cache_stats("miss")
                    return None
            except Exception as e:
                print(f"获取缓存资源时出错: key={key}, 错误={str(e)}")
                return None
        except Exception as e:
            await self._increment_cache_stats("error")
            print(f"获取缓存资源失败: key={key}, 错误={str(e)}")
            return None

    async def delete_cache(self, key: str) -> None:
        if not self.redis:
            return
        try:
            await self.redis.delete(key)
            print(f"缓存删除成功: {key}")
        except Exception as e:
            print(f"删除缓存失败: key={key}, 错误={str(e)}")

    async def cache_collection(self, collection_name: str, items: List[Any], filters: Dict[str, Any] = None, expire: int = CACHE_TTL) -> None:
        if not self.redis:
            return
        try:
            key = self._get_collection_cache_key(collection_name, **(filters or {}))
            serialized_items = []
            for item in items:
                if hasattr(item, 'to_dict'):
                    serialized_items.append(item.to_dict())
                elif hasattr(item, 'model_dump'):
                    serialized_items.append(item.model_dump())
                else:
                    serialized_items.append(item)
            await self.redis.set(key, json.dumps(serialized_items, ensure_ascii=False), ex=expire)
            print(f"集合缓存成功: {key}, count={len(items)}")
        except Exception as e:
            await self._increment_cache_stats("error")
            print(f"缓存集合失败: name={collection_name}, 错误={str(e)}")

    async def get_cached_collection(self, collection_name: str, deserializer: Callable[[List[Dict[str, Any]]], List[Any]], filters: Dict[str, Any] = None) -> Optional[List[Any]]:
        if not self.redis:
            return None
        try:
            key = self._get_collection_cache_key(collection_name, **(filters or {}))
            data = await self.redis.get(key)
            if data:
                await self._increment_cache_stats("hit")
                return deserializer(json.loads(data))
            else:
                await self._increment_cache_stats("miss")
                return None
        except Exception as e:
            await self._increment_cache_stats("error")
            print(f"获取缓存集合失败: name={collection_name}, 错误={str(e)}")
            return None

    async def get_cache_stats(self) -> Dict[str, int]:
        if not self.redis:
            return {"hit": 0, "miss": 0, "error": 0}
        try:
            stats_key = self._get_cache_stats_key()
            stats = await self.redis.hgetall(stats_key)
            return {
                "hit": int(stats.get(b"hit", 0)),
                "miss": int(stats.get(b"miss", 0)),
                "error": int(stats.get(b"error", 0))
            }
        except Exception as e:
            print(f"获取缓存统计失败: 错误={str(e)}", exc_info=True)
            return {"hit": 0, "miss": 0, "error": 0} 