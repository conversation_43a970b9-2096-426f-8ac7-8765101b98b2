from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, ConfigDict, Field
from svc.core.models.base import CamelCaseModel
from svc.core.schemas.base import PaginatedResponse

class SpecBase(CamelCaseModel):
    """规格基础模型"""
    model_config = ConfigDict(str_strip_whitespace=True)
    name: str = Field(..., max_length=200, description="规格名称")
    description: Optional[str] = Field(None, description="规格描述")
    sort_order: int = Field(default=0, description="排序权重")
    status: Optional[str] = Field(default="active", description="规格状态")
    meta_data: Dict[str, Any] = Field(default_factory=dict, description="元数据")

class SpecCreate(SpecBase):
    """规格创建模型"""
    pass

class SpecUpdate(CamelCaseModel):
    """规格更新模型"""
    model_config = ConfigDict(str_strip_whitespace=True)
    name: Optional[str] = Field(None, max_length=200, description="规格名称")
    description: Optional[str] = Field(None, description="规格描述")
    sort_order: Optional[int] = Field(default=None, description="排序权重")
    status: Optional[str] = Field(default=None, description="规格状态")
    meta_data: Optional[Dict[str, Any]] = Field(default=None, description="元数据")

class SpecResponse(SpecBase):
    """规格响应模型"""
    model_config = ConfigDict(from_attributes=True, str_strip_whitespace=True)
    id: int = Field(..., description="规格ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    deleted_at: Optional[datetime] = Field(default=None, description="删除时间")

class SpecListResponse(PaginatedResponse[SpecResponse]):
    """规格列表响应模型"""
    pass

class SpecOptionBase(CamelCaseModel):
    """规格选项基础模型"""
    model_config = ConfigDict(str_strip_whitespace=True)
    spec_id: int = Field(..., description="所属规格ID")
    value: str = Field(..., max_length=200, description="规格值")
    sort_order: int = Field(default=0, description="排序权重")
    status: Optional[str] = Field(default="active", description="规格选项状态")
    meta_data: Dict[str, Any] = Field(default_factory=dict, description="元数据")

class SpecOptionCreate(SpecOptionBase):
    """规格选项创建模型"""
    pass

class SpecOptionUpdate(CamelCaseModel):
    """规格选项更新模型"""
    model_config = ConfigDict(str_strip_whitespace=True)
    value: Optional[str] = Field(None, max_length=200, description="规格值")
    sort_order: Optional[int] = Field(default=None, description="排序权重")
    status: Optional[str] = Field(default=None, description="规格选项状态")
    meta_data: Optional[Dict[str, Any]] = Field(default=None, description="元数据")

class SpecOptionResponse(SpecOptionBase):
    """规格选项响应模型"""
    model_config = ConfigDict(from_attributes=True, str_strip_whitespace=True)
    id: int = Field(..., description="规格选项ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    deleted_at: Optional[datetime] = Field(default=None, description="删除时间")

class SpecOptionListResponse(PaginatedResponse[SpecOptionResponse]):
    """规格选项列表响应模型"""
    pass 