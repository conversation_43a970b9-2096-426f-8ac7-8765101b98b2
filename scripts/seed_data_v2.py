#!/usr/bin/env python3
"""
种子数据脚本 V2.0 - 深度重构版本
基于企业级架构设计，提供高性能、高可靠性的种子数据创建解决方案

主要特性：
- 依赖图管理和并行执行
- 分层事务管理和错误恢复
- 进度跟踪和断点续传
- 配置化管理和环境适配
- 性能监控和详细报告
"""
import argparse
import asyncio
import logging
import os
import sys
from pathlib import Path
from typing import List, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.core.orchestrator import SeedDataOrchestrator
from scripts.core.configuration import ConfigurationManager
from scripts.core.interfaces import DataType
from scripts.factories.permission_factory import PermissionFactory
from svc.core.database.session_utils import get_session_for_script

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/seed_data_v2.log', mode='a')
    ]
)
logger = logging.getLogger('seed_data_v2')


class SeedDataApplication:
    """种子数据应用程序主类"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_manager = ConfigurationManager(config_file)
        self.orchestrator: Optional[SeedDataOrchestrator] = None
        
    async def initialize(self) -> None:
        """初始化应用程序"""
        logger.info("初始化种子数据应用程序...")
        
        # 获取数据库会话
        db = await get_session_for_script()
        
        # 创建编排器
        self.orchestrator = SeedDataOrchestrator(db, self.config_manager)
        
        # 注册数据工厂
        await self._register_factories()
        
        logger.info("应用程序初始化完成")
    
    async def _register_factories(self) -> None:
        """注册所有数据工厂"""
        logger.info("注册数据工厂...")
        
        # 目前只实现了权限工厂作为示例
        # 其他工厂需要根据相同模式实现
        factories = [
            PermissionFactory(self.orchestrator.db),
            # TODO: 添加其他工厂
            # RoleFactory(self.orchestrator.db),
            # UserFactory(self.orchestrator.db),
            # CategoryFactory(self.orchestrator.db),
            # ProductFactory(self.orchestrator.db),
            # ShopFactory(self.orchestrator.db),
            # 等等...
        ]
        
        self.orchestrator.register_factories(factories)
        logger.info(f"已注册 {len(factories)} 个数据工厂")
    
    async def execute_full_seed(
        self, 
        execution_id: Optional[str] = None,
        resume: bool = False
    ) -> None:
        """执行完整的种子数据创建"""
        if not self.orchestrator:
            raise RuntimeError("应用程序未初始化")
        
        logger.info("开始执行完整种子数据创建...")
        
        try:
            results = await self.orchestrator.execute_full_seed(execution_id, resume)
            
            # 输出执行结果
            self._print_execution_results(results)
            
            # 生成执行报告
            if execution_id:
                report = await self.orchestrator.get_execution_report(execution_id)
                self._save_execution_report(report)
            
        except Exception as e:
            logger.error(f"执行失败: {e}")
            raise
    
    async def execute_partial_seed(
        self, 
        data_types: List[str],
        execution_id: Optional[str] = None
    ) -> None:
        """执行部分种子数据创建"""
        if not self.orchestrator:
            raise RuntimeError("应用程序未初始化")
        
        # 转换字符串为DataType枚举
        try:
            enum_types = [DataType(dt) for dt in data_types]
        except ValueError as e:
            logger.error(f"无效的数据类型: {e}")
            return
        
        logger.info(f"开始执行部分种子数据创建: {data_types}")
        
        try:
            results = await self.orchestrator.execute_partial_seed(enum_types, execution_id)
            self._print_execution_results(results)
            
        except Exception as e:
            logger.error(f"执行失败: {e}")
            raise
    
    async def cleanup_all_data(self) -> None:
        """清理所有种子数据"""
        if not self.orchestrator:
            raise RuntimeError("应用程序未初始化")
        
        logger.warning("即将清理所有种子数据，这是不可逆操作！")
        
        # 在生产环境中应该添加确认机制
        confirm = input("确认清理所有数据？(yes/no): ")
        if confirm.lower() != 'yes':
            logger.info("操作已取消")
            return
        
        try:
            await self.orchestrator.cleanup_all_data()
            logger.info("数据清理完成")
        except Exception as e:
            logger.error(f"数据清理失败: {e}")
            raise
    
    async def validate_data_integrity(self) -> None:
        """验证数据完整性"""
        if not self.orchestrator:
            raise RuntimeError("应用程序未初始化")
        
        logger.info("开始验证数据完整性...")
        
        try:
            results = await self.orchestrator.validate_data_integrity()
            
            print("\n=== 数据完整性检查结果 ===")
            for data_type, is_valid in results.items():
                status = "✓ 通过" if is_valid else "✗ 失败"
                print(f"{data_type.value:20} {status}")
            
            total_types = len(results)
            valid_types = sum(results.values())
            print(f"\n总计: {valid_types}/{total_types} 通过")
            
        except Exception as e:
            logger.error(f"完整性验证失败: {e}")
            raise
    
    def _print_execution_results(self, results) -> None:
        """打印执行结果"""
        print("\n=== 执行结果 ===")
        
        for data_type, result in results.items():
            status_icon = {
                "completed": "✓",
                "failed": "✗",
                "skipped": "⊝",
                "running": "⟳"
            }.get(result.status.value, "?")
            
            print(f"{status_icon} {data_type.value:20} "
                  f"状态: {result.status.value:10} "
                  f"记录数: {result.created_count:6} "
                  f"耗时: {result.execution_time:.2f}s")
            
            if result.error_message:
                print(f"   错误: {result.error_message}")
        
        # 统计信息
        total_types = len(results)
        completed = len([r for r in results.values() if r.status.value == "completed"])
        failed = len([r for r in results.values() if r.status.value == "failed"])
        skipped = len([r for r in results.values() if r.status.value == "skipped"])
        total_records = sum(r.created_count for r in results.values())
        total_time = sum(r.execution_time for r in results.values())
        
        print(f"\n=== 统计信息 ===")
        print(f"总数据类型: {total_types}")
        print(f"成功: {completed}, 失败: {failed}, 跳过: {skipped}")
        print(f"总记录数: {total_records}")
        print(f"总耗时: {total_time:.2f}s")
    
    def _save_execution_report(self, report) -> None:
        """保存执行报告"""
        import json
        
        report_file = f"logs/execution_report_{report['execution_id']}.json"
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"执行报告已保存到: {report_file}")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="种子数据脚本 V2.0")
    parser.add_argument("--config", help="配置文件路径")
    parser.add_argument("--execution-id", help="执行ID（用于恢复）")
    parser.add_argument("--resume", action="store_true", help="恢复执行")
    
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 完整执行命令
    subparsers.add_parser("full", help="执行完整的种子数据创建")
    
    # 部分执行命令
    partial_parser = subparsers.add_parser("partial", help="执行部分种子数据创建")
    partial_parser.add_argument("types", nargs="+", help="要创建的数据类型")
    
    # 清理命令
    subparsers.add_parser("cleanup", help="清理所有种子数据")
    
    # 验证命令
    subparsers.add_parser("validate", help="验证数据完整性")
    
    # 信息命令
    subparsers.add_parser("info", help="显示配置和依赖信息")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 创建应用程序实例
    app = SeedDataApplication(args.config)
    
    try:
        await app.initialize()
        
        if args.command == "full":
            await app.execute_full_seed(args.execution_id, args.resume)
        elif args.command == "partial":
            await app.execute_partial_seed(args.types, args.execution_id)
        elif args.command == "cleanup":
            await app.cleanup_all_data()
        elif args.command == "validate":
            await app.validate_data_integrity()
        elif args.command == "info":
            if app.orchestrator:
                stats = app.orchestrator.get_execution_statistics()
        
    except Exception as e:
        logger.error(f"应用程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # 确保日志目录存在
    os.makedirs("logs", exist_ok=True)
    
    # 运行主函数
    asyncio.run(main())
