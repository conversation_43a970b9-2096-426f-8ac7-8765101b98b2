"""基础仓储模式实现。
提供通用的数据访问方法，所有特定模型的仓储类都应该继承这个基类。
支持基本的CRUD操作、复杂过滤查询和高性能批量操作。
"""
from dataclasses import dataclass
from enum import Enum
from typing import (Any, Callable, Dict, Generic, List, Optional, Tuple, Type,
                    TypeVar, Union)

from sqlalchemy import (Column, and_, asc, delete, desc, func, or_, select,
                        update)
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import operators
from sqlalchemy.sql.expression import Select

# 定义模型类型变量
ModelType = TypeVar("ModelType")
# 定义创建数据类型变量（可选）
CreateSchemaType = TypeVar("CreateSchemaType")
# 定义更新数据类型变量（可选）
UpdateSchemaType = TypeVar("UpdateSchemaType")


class FilterOperator(str, Enum):
    """过滤操作符枚举"""
    EQ = "eq"          # 等于
    NE = "ne"          # 不等于
    GT = "gt"          # 大于
    GTE = "gte"        # 大于等于
    LT = "lt"          # 小于
    LTE = "lte"        # 小于等于
    LIKE = "like"      # 模糊匹配
    ILIKE = "ilike"    # 忽略大小写模糊匹配
    IN = "in"          # 包含于
    NOT_IN = "not_in"  # 不包含于
    IS_NULL = "is_null"        # 为空
    IS_NOT_NULL = "is_not_null" # 不为空
    BETWEEN = "between"         # 范围查询


@dataclass
class FilterCondition:
    """过滤条件"""
    field: str
    operator: FilterOperator
    value: Any


class FilterBuilder:
    """复杂过滤条件构建器"""
    
    def __init__(self):
        self.conditions: List[FilterCondition] = []
        self.logic_operator: str = "and"  # "and" 或 "or"
    
    def add_condition(self, field: str, operator: FilterOperator, value: Any) -> 'FilterBuilder':
        """添加过滤条件"""
        self.conditions.append(FilterCondition(field, operator, value))
        return self
    
    def set_logic_operator(self, operator: str) -> 'FilterBuilder':
        """设置逻辑操作符"""
        self.logic_operator = operator
        return self
    

    
    def build_sql_condition(self, column, condition: FilterCondition):
        """构建SQL条件"""
        try:
            if condition.operator == FilterOperator.EQ:
                return column == condition.value
            elif condition.operator == FilterOperator.NE:
                return column != condition.value
            elif condition.operator == FilterOperator.GT:
                return column > condition.value
            elif condition.operator == FilterOperator.GTE:
                return column >= condition.value
            elif condition.operator == FilterOperator.LT:
                return column < condition.value
            elif condition.operator == FilterOperator.LTE:
                return column <= condition.value
            elif condition.operator == FilterOperator.LIKE:
                return column.like(condition.value)
            elif condition.operator == FilterOperator.ILIKE:
                return column.ilike(condition.value)
            elif condition.operator == FilterOperator.IN:
                if isinstance(condition.value, (list, tuple)) and condition.value:
                    return column.in_(condition.value)
            elif condition.operator == FilterOperator.NOT_IN:
                if isinstance(condition.value, (list, tuple)) and condition.value:
                    return ~column.in_(condition.value)
            elif condition.operator == FilterOperator.IS_NULL:
                return column.is_(None)
            elif condition.operator == FilterOperator.IS_NOT_NULL:
                return column.is_not(None)
            elif condition.operator == FilterOperator.BETWEEN:
                if isinstance(condition.value, (list, tuple)) and len(condition.value) == 2:
                    return column.between(condition.value[0], condition.value[1])
        except Exception:
            # 如果构建条件时出错，返回None
            pass

        return None
    
    @classmethod
    def from_dict(cls, filters: Dict[str, Any]) -> 'FilterBuilder':
        """从字典创建过滤器
        
        支持格式:
        - 简单格式: {'name': 'test', 'age': 18}
        - 操作符格式: {'name__like': '%test%', 'age__gte': 18}
        """
        builder = cls()
        
        for key, value in filters.items():
            if '__' in key:
                field, op_str = key.rsplit('__', 1)
                try:
                    operator = FilterOperator(op_str)
                except ValueError:
                    # 如果操作符无效，使用等于操作
                    field = key
                    operator = FilterOperator.EQ
            else:
                field = key
                operator = FilterOperator.EQ
            
            builder.add_condition(field, operator, value)
        
        return builder

class BaseRepository(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """
    通用仓储基类，提供基本的CRUD操作、复杂过滤查询和高性能批量操作。
    所有模型特定的仓储类都应该继承这个基类。
    
    类型参数:
        ModelType: 数据库模型类型（必需）
        CreateSchemaType: 创建数据模式类型（可选）
        UpdateSchemaType: 更新数据模式类型（可选）
        
    用法示例:
        1. 基本用法（只使用模型类型）:
           ```python
           class UserRepository(BaseRepository[User]):
               pass
           ```
        
        2. 完整用法（包含创建和更新模式）:
           ```python
           class UserRepository(BaseRepository[User, UserCreate, UserUpdate]):
               pass
           ```
           
        3. 统一的过滤查询API:
           ```python
           # 简单过滤
           users = await repo.get_list(name="John", age=25)
           
           # 高级过滤（字典格式）
           users = await repo.get_list(filters={
               'name__like': '%John%',
               'age__gte': 18,
               'status__in': ['active', 'pending']
           })
           
           # 使用FilterBuilder
           builder = FilterBuilder().add_condition('name', FilterOperator.LIKE, '%John%')
           users = await repo.get_list(filters=builder)
           
           # 分页查询（支持所有过滤方式）
           items, total = await repo.get_paginated(
               page_num=1, page_size=10, filters=builder
           )
           
           # 计数查询（支持所有过滤方式）
           count = await repo.count(filters={'status': 'active'})
           
           # 存在性检查（支持所有过滤方式）
           exists = await repo.exists(name="John")
           ```
    """
    
    def __init__(self, db: AsyncSession, model: Type[ModelType]):
        """
        初始化仓储基类

        Args:
            db: 数据库会话
            model: SQLAlchemy模型类
        """
        self.db = db
        self.model = model
        self._datetime_utils = None  # 延迟导入
    
    def _get_datetime_utils(self):
        """延迟导入datetime工具"""
        if self._datetime_utils is None:
            from svc.core.utils.datetime_utils import \
                get_utc_now_without_tzinfo
            self._datetime_utils = get_utc_now_without_tzinfo
        return self._datetime_utils
    
    def _validate_field_exists(self, field: str) -> bool:
        """验证字段是否存在于模型中"""
        if not field or not isinstance(field, str):
            return False
        return hasattr(self.model, field)
    
    def _prepare_data(self, data: Any, exclude_none: bool = True) -> Dict[str, Any]:
        """预处理数据，支持Pydantic对象和字典"""
        if hasattr(data, 'model_dump'):
            model_data = data.model_dump(exclude_none=exclude_none)
        elif isinstance(data, dict):
            model_data = data.copy()
            if exclude_none:
                model_data = {k: v for k, v in model_data.items() if v is not None}
        else:
            model_data = data
        
        # 只保留模型中存在的字段
        return {k: v for k, v in model_data.items() if self._validate_field_exists(k)}
    
    def _add_timestamps(self, data: Dict[str, Any], is_update: bool = False) -> Dict[str, Any]:
        """添加时间戳字段"""
        get_now = self._get_datetime_utils()
        
        if not is_update and self._validate_field_exists('created_at') and 'created_at' not in data:
            data['created_at'] = get_now()
        
        if self._validate_field_exists('updated_at') and 'updated_at' not in data:
            data['updated_at'] = get_now()
        
        return data

    def _apply_filters(self, query: Select, **filters) -> Select:
        """应用简单过滤条件（字段名=值）"""
        for field, value in filters.items():
            if self._validate_field_exists(field):
                column = getattr(self.model, field)
                query = query.where(column == value)
        return query

    def _apply_advanced_filters(self, query: Select, filters: Dict[str, Any]) -> Select:
        """应用高级过滤条件（支持操作符）"""
        filter_builder = FilterBuilder.from_dict(filters)
        return self._apply_filter_builder(query, filter_builder)

    def _apply_filter_builder(self, query: Select, filter_builder: FilterBuilder) -> Select:
        """应用FilterBuilder对象"""
        conditions = []
        for condition in filter_builder.conditions:
            if self._validate_field_exists(condition.field):
                column = getattr(self.model, condition.field)
                sql_condition = filter_builder.build_sql_condition(column, condition)
                if sql_condition is not None:
                    conditions.append(sql_condition)

        if conditions:
            if filter_builder.logic_operator == "or":
                query = query.where(or_(*conditions))
            else:
                query = query.where(and_(*conditions))
        return query

    async def get_by_id(
        self,
        id: Any,
        id_field: str = "id",
        include_deleted: bool = False
    ) -> Optional[ModelType]:
        """
        通过ID获取实体

        Args:
            id: 实体ID
            id_field: ID字段名，默认为'id'
            include_deleted: 是否包含软删除的记录

        Returns:
            Optional[ModelType]: 实体对象，不存在则返回None
        """
        if not id or not self._validate_field_exists(id_field):
            return None

        filters = {id_field: id}
        if not include_deleted and self._validate_field_exists('deleted_at'):
            filters['deleted_at'] = None

        return await self.get_one(**filters)
    
    async def get_one(
        self, 
        **filters
    ) -> Optional[ModelType]:
        """
        根据条件获取单个实体
        
        Args:
            db: 数据库会话
            **filters: 过滤条件，格式为字段名=值
            
        Returns:
            Optional[ModelType]: 实体对象，不存在则返回None
        """
        if not filters:
            return None # 没有过滤条件，无法确定唯一实体
        
        query = select(self.model)
        query = self._apply_filters(query, **filters)
        
        result = await self.db.execute(query)
        return result.scalars().first()
    
    async def get_list(
        self,
        *,
        skip: int = 0,
        limit: int = 100,
        order_by: Optional[Union[str, Column]] = None,
        order_direction: str = "asc",
        filters: Optional[Union[Dict[str, Any], FilterBuilder]] = None,
        include_deleted: bool = False,
        **simple_filters
    ) -> List[ModelType]:
        """
        获取实体列表（支持简单和高级过滤）

        Args:
            skip: 跳过记录数
            limit: 返回记录数上限
            order_by: 排序字段，可以是字段名字符串或SQLAlchemy列对象
            order_direction: 排序方向，'asc'升序或'desc'降序
            filters: 高级过滤条件字典或FilterBuilder对象
            include_deleted: 是否包含软删除的记录
            **simple_filters: 简单过滤条件，格式为字段名=值

        Returns:
            List[ModelType]: 实体列表
        """
        # 确保 skip 不为负数，limit 有合理上限
        safe_skip = max(0, skip)
        safe_limit = min(max(1, limit), 10000)  # 限制最大查询数量

        query = select(self.model)

        # 默认排除软删除记录
        if not include_deleted and self._validate_field_exists('deleted_at'):
            query = query.where(self.model.deleted_at.is_(None))

        # 应用过滤条件
        if isinstance(filters, FilterBuilder):
            query = self._apply_filter_builder(query, filters)
        elif isinstance(filters, dict):
            query = self._apply_advanced_filters(query, filters)

        if simple_filters:
            query = self._apply_filters(query, **simple_filters)

        # 添加排序
        if order_by:
            order_column = None
            if isinstance(order_by, str) and self._validate_field_exists(order_by):
                order_column = getattr(self.model, order_by)
            elif isinstance(order_by, Column):
                order_column = order_by

            if order_column is not None:
                if order_direction.lower() == "desc":
                    query = query.order_by(desc(order_column))
                else:
                    query = query.order_by(asc(order_column))

        # 添加分页
        query = query.offset(safe_skip).limit(safe_limit)

        # 执行查询
        result = await self.db.execute(query)
        return result.scalars().all()
    

    
    async def count(
        self,
        filters: Optional[Union[Dict[str, Any], FilterBuilder]] = None,
        include_deleted: bool = False,
        **simple_filters
    ) -> int:
        """
        计算符合条件的记录数量（支持简单和高级过滤）

        Args:
            filters: 高级过滤条件字典或FilterBuilder对象
            include_deleted: 是否包含软删除的记录
            **simple_filters: 简单过滤条件，格式为字段名=值

        Returns:
            int: 记录数量
        """
        # 优化计数查询，使用count(*)而不是count(column)
        query = select(func.count()).select_from(self.model)

        # 默认排除软删除记录
        if not include_deleted and self._validate_field_exists('deleted_at'):
            query = query.where(self.model.deleted_at.is_(None))

        # 应用过滤条件
        if isinstance(filters, FilterBuilder):
            query = self._apply_filter_builder(query, filters)
        elif isinstance(filters, dict):
            query = self._apply_advanced_filters(query, filters)

        if simple_filters:
            query = self._apply_filters(query, **simple_filters)

        # 执行查询
        result = await self.db.execute(query)
        count = result.scalar()
        return count if count is not None else 0
    
    async def get_paginated(
        self,
        *,
        page_num: int = 1,
        page_size: int = 10,
        order_by: Optional[Union[str, Column]] = None,
        order_direction: str = "asc",
        with_total: bool = True,
        filters: Optional[Union[Dict[str, Any], FilterBuilder]] = None,
        include_deleted: bool = False,
        **simple_filters
    ) -> Tuple[List[ModelType], Optional[int]]:
        """
        获取分页的实体列表及总数（支持简单和高级过滤）

        Args:
            page_num: 页码，从1开始
            page_size: 每页大小
            order_by: 排序字段
            order_direction: 排序方向
            with_total: 是否查询总数
            filters: 高级过滤条件字典或FilterBuilder对象
            include_deleted: 是否包含软删除的记录
            **simple_filters: 简单过滤条件

        Returns:
            (items, total) 元组，total为None表示未查总数
        """
        # 确保页码和页大小的合理性
        safe_page_num = max(1, page_num)
        safe_page_size = min(max(1, page_size), 1000)  # 限制最大页大小
        skip = (safe_page_num - 1) * safe_page_size

        items = await self.get_list(
            skip=skip,
            limit=safe_page_size,
            order_by=order_by,
            order_direction=order_direction,
            filters=filters,
            include_deleted=include_deleted,
            **simple_filters
        )

        total = None
        if with_total:
            total = await self.count(
                filters=filters,
                include_deleted=include_deleted,
                **simple_filters
            )

        return items, total
    

    
    async def exists(
        self,
        filters: Optional[Union[Dict[str, Any], FilterBuilder]] = None,
        include_deleted: bool = False,
        **simple_filters
    ) -> bool:
        """
        检查是否存在符合条件的记录

        Args:
            filters: 高级过滤条件字典或FilterBuilder对象
            include_deleted: 是否包含软删除的记录
            **simple_filters: 简单过滤条件

        Returns:
            bool: 存在返回True，否则返回False
        """
        # 使用EXISTS查询优化性能
        subquery = select(1).select_from(self.model)

        # 默认排除软删除记录
        if not include_deleted and self._validate_field_exists('deleted_at'):
            subquery = subquery.where(self.model.deleted_at.is_(None))

        # 应用过滤条件
        if isinstance(filters, FilterBuilder):
            subquery = self._apply_filter_builder(subquery, filters)
        elif isinstance(filters, dict):
            subquery = self._apply_advanced_filters(subquery, filters)

        if simple_filters:
            subquery = self._apply_filters(subquery, **simple_filters)

        # 使用EXISTS查询
        exists_query = select(subquery.exists())
        result = await self.db.execute(exists_query)
        return result.scalar() or False
    
    async def get_first(
        self,
        order_by: Optional[Union[str, Column]] = None,
        order_direction: str = "asc",
        **filters
    ) -> Optional[ModelType]:
        """
        获取第一个符合条件的记录
        
        Args:
            order_by: 排序字段
            order_direction: 排序方向
            **filters: 过滤条件
            
        Returns:
            Optional[ModelType]: 第一个记录，不存在则返回None
        """
        results = await self.get_list(
            skip=0,
            limit=1,
            order_by=order_by,
            order_direction=order_direction,
            **filters
        )
        return results[0] if results else None
    
    async def get_last(
        self,
        order_by: Optional[Union[str, Column]] = None,
        **filters
    ) -> Optional[ModelType]:
        """
        获取最后一个符合条件的记录
        
        Args:
            order_by: 排序字段，默认使用id
            **filters: 过滤条件
            
        Returns:
            Optional[ModelType]: 最后一个记录，不存在则返回None
        """
        if not order_by:
            order_by = 'id' if self._validate_field_exists('id') else None
        
        if not order_by:
            return None
            
        results = await self.get_list(
            skip=0,
            limit=1,
            order_by=order_by,
            order_direction="desc",
            **filters
        )
        return results[0] if results else None
    
    async def create(
        self, 
        data: Any
    ) -> ModelType:
        """
        创建新实体
        
        Args:
            data: 实体数据字典或Pydantic对象
            
        Returns:
            ModelType: 创建的实体对象
        """
        # 预处理数据并添加时间戳
        model_data = self._prepare_data(data)
        model_data = self._add_timestamps(model_data, is_update=False)
        
        obj = self.model(**model_data)
        self.db.add(obj)
        await self.db.flush()
        await self.db.refresh(obj)
        return obj
    
    async def update(
        self, 
        db_obj: ModelType, 
        data: Dict[str, Any]
    ) -> ModelType:
        """
        更新实体
        
        Args:
            db_obj: 要更新的实体对象
            data: 更新数据字典
            
        Returns:
            ModelType: 更新后的实体对象
        """
        # 预处理数据并添加时间戳
        update_data = self._prepare_data(data)
        update_data = self._add_timestamps(update_data, is_update=True)
        
        # 更新对象属性
        for field, value in update_data.items():
            if hasattr(db_obj, field):
                setattr(db_obj, field, value)
        
        await self.db.flush()
        await self.db.refresh(db_obj)
        return db_obj
    
    async def update_by_id(
        self, 
        id: Any, 
        data: Dict[str, Any],
        id_field: str = "id"
    ) -> Optional[ModelType]:
        """
        通过ID更新实体
        
        Args:
            id: 实体ID
            data: 更新数据字典
            id_field: ID字段名，默认为'id'
            
        Returns:
            Optional[ModelType]: 更新后的实体对象，不存在则返回None
        """
        db_obj = await self.get_by_id(id, id_field=id_field)
        if not db_obj:
            return None
        
        return await self.update(db_obj, data)
    
    async def delete(
        self, 
        db_obj: ModelType
    ) -> None:
        """
        删除实体
        
        Args:
            db_obj: 要删除的实体对象
        """
        await self.db.delete(db_obj)
        await self.db.flush()
    
    async def delete_by_id(
        self, 
        id: Any,
        id_field: str = "id"
    ) -> bool:
        """
        通过ID删除实体
        
        Args:
            id: 实体ID
            id_field: ID字段名，默认为'id'
            
        Returns:
            bool: 删除成功返回True，否则返回False
        """
        db_obj = await self.get_by_id(id, id_field=id_field)
        if not db_obj:
            return False
        
        await self.delete(db_obj)
        return True
    


    async def get_existing_ids(self, resource_ids: List[int], id_field: str = "id") -> List[int]:
        """获取存在的资源ID列表

        Args:
            resource_ids: 资源ID列表
            id_field: ID字段名，默认为'id'

        Returns:
            List[int]: 存在的资源ID列表
        """
        if not resource_ids:
            return []

        if not hasattr(self.model, id_field):
            return []

        id_column = getattr(self.model, id_field)
        query = select(id_column).where(id_column.in_(resource_ids))
        result = await self.db.execute(query)
        return result.scalars().all()

    async def batch_update(
        self,
        resource_ids: List[int],
        update_data: Dict[str, Any],
        id_field: str = "id",
        auto_update_timestamp: bool = True,
        batch_size: int = 1000,
        exclude_deleted: bool = True
    ) -> int:
        """批量更新资源（优化版）

        Args:
            resource_ids: 资源ID列表
            update_data: 更新数据字典
            id_field: ID字段名，默认为'id'
            auto_update_timestamp: 是否自动更新时间戳
            batch_size: 批次大小，默认1000
            exclude_deleted: 是否排除软删除的记录

        Returns:
            int: 更新的记录数
        """
        if not resource_ids or not update_data:
            return 0

        if not self._validate_field_exists(id_field):
            raise ValueError(f"Field '{id_field}' does not exist in model {self.model.__name__}")

        # 预处理数据
        filtered_data = self._prepare_data(update_data)
        if auto_update_timestamp:
            filtered_data = self._add_timestamps(filtered_data, is_update=True)

        if not filtered_data:
            return 0

        # 分批处理以提高性能
        total_updated = 0
        id_column = getattr(self.model, id_field)
        safe_batch_size = min(max(1, batch_size), 5000)  # 限制批次大小

        for i in range(0, len(resource_ids), safe_batch_size):
            batch_ids = resource_ids[i:i + safe_batch_size]

            # 构建更新条件
            conditions = [id_column.in_(batch_ids)]
            if exclude_deleted and self._validate_field_exists('deleted_at'):
                conditions.append(self.model.deleted_at.is_(None))

            stmt = update(self.model).where(and_(*conditions)).values(**filtered_data)
            result = await self.db.execute(stmt)
            total_updated += result.rowcount

        await self.db.flush()
        return total_updated

    async def bulk_create(
        self,
        data_list: List[Dict[str, Any]],
        batch_size: int = 1000,
        return_objects: bool = False,
        ignore_conflicts: bool = False
    ) -> Union[List[ModelType], int]:
        """批量创建资源（优化版）

        Args:
            data_list: 数据字典列表
            batch_size: 批次大小，默认1000
            return_objects: 是否返回创建的对象，默认False返回创建数量
            ignore_conflicts: 是否忽略冲突（如主键重复），默认False

        Returns:
            Union[List[ModelType], int]: 创建的对象列表或创建数量
        """
        if not data_list:
            return [] if return_objects else 0

        # 预处理所有数据
        filtered_data = []
        for data in data_list:
            processed_data = self._prepare_data(data)
            processed_data = self._add_timestamps(processed_data, is_update=False)
            if processed_data:  # 只添加非空数据
                filtered_data.append(processed_data)

        if not filtered_data:
            return [] if return_objects else 0

        safe_batch_size = min(max(1, batch_size), 5000)  # 限制批次大小

        if return_objects:
            # 如果需要返回对象，使用传统方式创建
            created_objects = []
            for i in range(0, len(filtered_data), safe_batch_size):
                batch = filtered_data[i:i + safe_batch_size]
                batch_objects = []

                try:
                    for item_data in batch:
                        obj = self.model(**item_data)
                        self.db.add(obj)
                        batch_objects.append(obj)

                    await self.db.flush()
                    # 刷新对象以获取生成的ID
                    for obj in batch_objects:
                        await self.db.refresh(obj)

                    created_objects.extend(batch_objects)
                except Exception as e:
                    if not ignore_conflicts:
                        raise e
                    # 如果忽略冲突，回滚当前批次
                    await self.db.rollback()

            return created_objects
        else:
            # 使用bulk insert提高性能
            total_created = 0
            for i in range(0, len(filtered_data), safe_batch_size):
                batch = filtered_data[i:i + safe_batch_size]
                try:
                    # 使用SQLAlchemy的insert语句
                    stmt = self.model.__table__.insert().values(batch)
                    await self.db.execute(stmt)
                    total_created += len(batch)
                except Exception as e:
                    if not ignore_conflicts:
                        raise e
                    # 如果忽略冲突，继续处理下一批
                    continue

            await self.db.flush()
            return total_created

    async def batch_delete(
        self,
        resource_ids: List[int],
        id_field: str = "id",
        soft_delete: bool = True,
        batch_size: int = 1000
    ) -> int:
        """批量删除资源（优化版）

        Args:
            resource_ids: 资源ID列表
            id_field: ID字段名，默认为'id'
            soft_delete: 是否软删除
            batch_size: 批次大小，默认1000

        Returns:
            int: 删除的记录数
        """
        if not resource_ids:
            return 0

        if not self._validate_field_exists(id_field):
            raise ValueError(f"Field '{id_field}' does not exist in model {self.model.__name__}")

        id_column = getattr(self.model, id_field)
        total_deleted = 0
        safe_batch_size = min(max(1, batch_size), 5000)  # 限制批次大小

        # 分批处理以提高性能
        for i in range(0, len(resource_ids), safe_batch_size):
            batch_ids = resource_ids[i:i + safe_batch_size]

            if soft_delete and self._validate_field_exists('deleted_at'):
                # 软删除：设置deleted_at字段
                update_data = {'deleted_at': self._get_datetime_utils()()}

                # 如果有is_active字段，也设置为False
                if self._validate_field_exists('is_active'):
                    update_data['is_active'] = False

                # 只更新未删除的记录
                stmt = update(self.model).where(
                    and_(
                        id_column.in_(batch_ids),
                        self.model.deleted_at.is_(None)
                    )
                ).values(**update_data)
            else:
                # 硬删除：直接删除记录
                stmt = delete(self.model).where(id_column.in_(batch_ids))

            result = await self.db.execute(stmt)
            total_deleted += result.rowcount

        await self.db.flush()
        return total_deleted

    async def restore(
        self,
        resource_ids: List[int],
        id_field: str = "id",
        batch_size: int = 1000
    ) -> int:
        """恢复软删除的资源

        Args:
            resource_ids: 资源ID列表
            id_field: ID字段名，默认为'id'
            batch_size: 批次大小，默认1000

        Returns:
            int: 恢复的记录数
        """
        if not resource_ids or not self._validate_field_exists('deleted_at'):
            return 0

        if not self._validate_field_exists(id_field):
            raise ValueError(f"Field '{id_field}' does not exist in model {self.model.__name__}")

        id_column = getattr(self.model, id_field)
        total_restored = 0
        safe_batch_size = min(max(1, batch_size), 5000)

        # 分批处理
        for i in range(0, len(resource_ids), safe_batch_size):
            batch_ids = resource_ids[i:i + safe_batch_size]

            update_data = {'deleted_at': None}
            if self._validate_field_exists('is_active'):
                update_data['is_active'] = True

            # 只恢复已删除的记录
            stmt = update(self.model).where(
                and_(
                    id_column.in_(batch_ids),
                    self.model.deleted_at.is_not(None)
                )
            ).values(**update_data)

            result = await self.db.execute(stmt)
            total_restored += result.rowcount

        await self.db.flush()
        return total_restored
    
    async def batch_upsert(
        self,
        data_list: List[Dict[str, Any]],
        conflict_columns: List[str],
        update_columns: Optional[List[str]] = None,
        batch_size: int = 500
    ) -> int:
        """批量插入或更新（upsert）操作（简化版）

        Args:
            data_list: 数据字典列表
            conflict_columns: 冲突检测列（如主键、唯一索引）
            update_columns: 冲突时需要更新的列，None表示更新所有列
            batch_size: 批次大小，默认500（upsert操作较复杂，使用较小批次）

        Returns:
            int: 影响的记录数
        """
        if not data_list or not conflict_columns:
            return 0

        # 验证冲突列是否存在
        for col in conflict_columns:
            if not self._validate_field_exists(col):
                raise ValueError(f"Conflict column '{col}' does not exist in model {self.model.__name__}")

        # 预处理数据
        filtered_data = []
        for data in data_list:
            processed_data = self._prepare_data(data)
            processed_data = self._add_timestamps(processed_data, is_update=False)
            if processed_data:
                filtered_data.append(processed_data)

        if not filtered_data:
            return 0

        total_affected = 0
        safe_batch_size = min(max(1, batch_size), 1000)

        # 分批处理，使用简化的upsert逻辑
        for i in range(0, len(filtered_data), safe_batch_size):
            batch = filtered_data[i:i + safe_batch_size]

            for item_data in batch:
                try:
                    # 先检查记录是否存在
                    conditions = []
                    for col in conflict_columns:
                        if col in item_data:
                            conditions.append(getattr(self.model, col) == item_data[col])

                    if not conditions:
                        continue

                    # 查询是否存在
                    exists_query = select(self.model).where(and_(*conditions))
                    result = await self.db.execute(exists_query)
                    existing = result.scalars().first()

                    if existing:
                        # 记录存在，执行更新
                        update_data = item_data.copy()
                        if update_columns:
                            update_data = {k: v for k, v in update_data.items() if k in update_columns}
                        update_data = self._add_timestamps(update_data, is_update=True)

                        # 移除冲突列，避免更新主键
                        for col in conflict_columns:
                            update_data.pop(col, None)

                        if update_data:
                            stmt = update(self.model).where(and_(*conditions)).values(**update_data)
                            result = await self.db.execute(stmt)
                            total_affected += result.rowcount
                    else:
                        # 记录不存在，执行插入
                        obj = self.model(**item_data)
                        self.db.add(obj)
                        await self.db.flush()
                        total_affected += 1

                except Exception as e:
                    # 发生错误时回滚当前操作
                    await self.db.rollback()
                    raise e

        await self.db.flush()
        return total_affected