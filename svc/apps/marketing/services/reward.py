from typing import List, Optional, Dict, Any, TypeVar, Generic
import logging
from sqlalchemy.ext.asyncio import AsyncSession
from redis.asyncio import Redis
import json

from svc.apps.marketing.repositories.invitation import InvitationRepository
from svc.apps.marketing.repositories.reward import RewardRecordRepository, RewardStrategyRepository
from svc.apps.marketing.schemas.reward import RewardRecordCreate, RewardRecordResponse, RewardStatsResponse, RewardStrategyResponse, RewardRecordListResponse, GetRewardRecordsParams
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo
from svc.core.services.base import BaseService
from svc.core.services.error_result_mixin import ErrorResultMixin
from svc.core.services.batch_operation_mixin import BatchOperationMixin
from svc.core.services.cache_mixin import CacheMixin
from svc.apps.auth import UserService

from svc.apps.marketing.models.reward import RewardStrategy, RewardRecord
from svc.apps.marketing.models.invitation import Invitation
from svc.apps.marketing.models.campaign import Campaign
from svc.core.models.result import Result
from svc.apps.marketing.services.campaign import CampaignService

from fastapi_events.dispatcher import dispatch
from svc.core.events.event_names import (
    SYSTEM_AUDIT_LOG_RECORDED, 
    SYSTEM_CACHE_INVALIDATION_REQUESTED,
    MARKETING_REWARD_ISSUED
)

# 定义泛型类型变量
T = TypeVar('T')

CACHE_TTL = 3600  # 1小时缓存过期时间

class RewardStrategyService(BaseService, ErrorResultMixin, BatchOperationMixin, CacheMixin):
    """
    奖励策略服务，负责奖励策略的创建、查询和管理
    """
    resource_type = "奖励策略"
    
    def __init__(
        self, 
        campaign_service: Optional[CampaignService] = None,
        redis: Optional[Redis] = None,
        reward_strategy_repo: Optional[RewardStrategyRepository] = None
    ):
        """初始化奖励策略服务
        
        Args:
            db: 数据库会话
            campaign_service: 活动服务实例（可选）
            redis: Redis客户端（可选）
            reward_strategy_repo: 奖励策略仓库实例（可选）
        """
        BaseService.__init__(self, redis)
        self.campaign_service = campaign_service
        self.reward_strategy_repo = reward_strategy_repo
    def _get_strategy_cache_key(self, strategy_id: int) -> str:
        """获取策略缓存键
        
        Args:
            strategy_id: 策略ID
            
        Returns:
            str: 缓存键
        """
        return f"reward_strategy:{strategy_id}"

    async def get_resource_by_id(self, strategy_id: int) -> Optional[RewardStrategy]:
        """
        获取指定ID的奖励策略
        
        Args:
            strategy_id: 策略ID
            
        Returns:
            Optional[RewardStrategy]: 策略对象，不存在时返回None
        """
        # 先尝试从缓存获取
        cache_key = self._get_strategy_cache_key(strategy_id)
        cached_strategy = await self.get_cached_resource(
            cache_key,
            lambda data: RewardStrategy.model_validate(data)
        )
        if cached_strategy:
            return cached_strategy
            
        # 缓存未命中，从数据库获取
        strategy = await self.reward_strategy_repo.get_by_id( strategy_id)
        
        # 如果找到策略，则缓存它
        if strategy:
            await self.cache_resource(cache_key, strategy, CACHE_TTL)
            
        return strategy
    
    async def get_campaign_strategies(
        self, 
        campaign_id: int,
        *,
        for_inviter: Optional[bool] = None,
        for_invitee: Optional[bool] = None,
        page_num: int = 1,
        page_size: int = 100
    ) -> Result[RewardStrategyResponse]:
        """
        获取活动的所有奖励策略
        
        Args:
            campaign_id: 活动ID
            for_inviter: 是否筛选邀请人的策略
            for_invitee: 是否筛选被邀请人的策略
            page_num: 页码
            page_size: 每页数量
            
        Returns:
            Result: 包含策略列表和分页信息的响应结果
        """
        try:
            self.logger.info(f"获取活动 {campaign_id} 的奖励策略: page={page_num}, size={page_size}, 过滤条件: for_inviter={for_inviter}, for_invitee={for_invitee}")
            
            strategies, total = await self.reward_strategy_repo.get_campaign_strategies(
                campaign_id,
                for_inviter=for_inviter,
                for_invitee=for_invitee,
                page_num=page_num,
                page_size=page_size
            )
            
            # 缓存所有策略
            for strategy in strategies:
                cache_key = self._get_strategy_cache_key(strategy.id)
                await self.cache_resource(cache_key, strategy, CACHE_TTL)
            
            # 计算总页数
            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0
            
            # 构建分页响应（假设使用通用分页响应）
            paginated_response = {
                "items": [RewardStrategyResponse.model_validate(s) for s in strategies],
                "total": total,
                "page": page_num,
                "size": page_size,
                "pages": total_pages
            }
            
            return self.create_success_result(paginated_response)
        except Exception as e:
            self.logger.exception(f"获取活动 {campaign_id} 的奖励策略失败: {str(e)}")
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取奖励策略失败: {str(e)}"
            )
    
    async def get_strategy(self, strategy_id: int) -> Optional[RewardStrategy]:
        """
        获取指定ID的奖励策略
        
        Args:
            strategy_id: 策略ID
            
        Returns:
            Optional[RewardStrategy]: 策略对象，不存在时返回None
        """
        self.logger.info(f"获取奖励策略 {strategy_id}")
        return await self.get_resource_by_id(strategy_id)
    
    async def create_strategy(
        self, 
        campaign_id: int,
        name: str,
        description: Optional[str] = None,
        reward_type: str = "fixed",
        is_for_inviter: bool = True,
        is_for_invitee: bool = False,
        base_reward: float = 0,
        percentage_rate: Optional[float] = None,
        tiered_config: Optional[str] = None,
        min_invitations: Optional[int] = None,
        max_rewards: Optional[int] = None
    ) -> Result[RewardStrategyResponse]:
        """
        创建奖励策略
        
        Args:
            campaign_id: 活动ID
            name: 策略名称
            description: 策略描述
            reward_type: 奖励类型
            is_for_inviter: 是否给邀请人
            is_for_invitee: 是否给被邀请人
            base_reward: 基础奖励值
            percentage_rate: 百分比率
            tiered_config: 阶梯配置
            min_invitations: 最小邀请数
            max_rewards: 最大奖励次数
            
        Returns:
            Result[RewardStrategyResponse]: 结果对象，包含创建的策略
        """
        # 验证活动是否存在
        self.logger.info(f"创建活动 {campaign_id} 的奖励策略: {name}")
        
        try:
            campaign = await self.campaign_service.get_campaign(campaign_id)
            if not campaign:
                self.logger.warning(f"活动 {campaign_id} 不存在")
                return self.create_error_result(
                    error_code=ErrorCode.CAMPAIGN_NOT_FOUND,
                    error_message="活动不存在"
                )
            
            # 创建策略
            strategy = await self.reward_strategy_repo.create_strategy(
                campaign_id=campaign_id,
                name=name,
                description=description,
                reward_type=reward_type,
                is_for_inviter=is_for_inviter,
                is_for_invitee=is_for_invitee,
                base_reward=base_reward,
                percentage_rate=percentage_rate,
                tiered_config=tiered_config,
                min_invitations=min_invitations,
                max_rewards=max_rewards
            )
            
            # 发送事件
            event_data = { 
                "strategy_id": strategy.id,
                "campaign_id": campaign_id,
                "name": name,
                "reward_type": reward_type
            }
            dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
                 "user_id": event_data.get("created_by"), # Assuming created_by in event_data or context
                 "action": "reward_strategy_created",
                 "resource_type": "reward_strategy",
                 "resource_id": strategy.id,
                 "metadata": event_data
            })
            
            dispatch(SYSTEM_CACHE_INVALIDATION_REQUESTED, payload={"resource_type": "reward_strategy", "resource_id": strategy.id})
            
            self.logger.info(f"成功创建奖励策略 {strategy.id}")
            return self.create_success_result(strategy)
        except Exception as e:
            self.logger.exception(f"创建奖励策略失败: {str(e)}")
            await self.db.rollback()
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"创建奖励策略失败: {str(e)}"
            )
    
    async def update_strategy(
        self,
        strategy_id: int,
        name: Optional[str] = None,
        description: Optional[str] = None,
        reward_type: Optional[str] = None,
        is_for_inviter: Optional[bool] = None,
        is_for_invitee: Optional[bool] = None,
        base_reward: Optional[float] = None,
        percentage_rate: Optional[float] = None,
        tiered_config: Optional[str] = None,
        min_invitations: Optional[int] = None,
        max_rewards: Optional[int] = None
    ) -> Result[RewardStrategyResponse]:
        """
        更新奖励策略
        
        Args:
            strategy_id: 策略ID
            其他参数: 可选，不传表示不更新
            
        Returns:
            Result[RewardStrategyResponse]: 结果对象，包含更新后的策略
        """
        self.logger.info(f"更新奖励策略 {strategy_id}")
        
        try:
            strategy = await self.get_strategy(strategy_id)
            if not strategy:
                self.logger.warning(f"奖励策略 {strategy_id} 不存在")
                return self.resource_not_found_result(strategy_id)
            
            # 更新策略
            strategy = await strategy.update(
                
                name=name,
                description=description,
                reward_type=reward_type,
                is_for_inviter=is_for_inviter,
                is_for_invitee=is_for_invitee,
                base_reward=base_reward,
                percentage_rate=percentage_rate,
                tiered_config=tiered_config,
                min_invitations=min_invitations,
                max_rewards=max_rewards
            )
            
            # 发送事件
            event_data = {
                "strategy_id": strategy.id,
                "updated_fields": list(strategy.dict(exclude={"id", "campaign_id"}).keys()),
                "strategy_name": strategy.name, # 记录名称以供参考
            }
            dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
                 "user_id": event_data.get("updated_by"), # Assuming updated_by in event_data or context
                 "action": "reward_strategy_updated",
                 "resource_type": "reward_strategy",
                 "resource_id": strategy.id,
                 "metadata": event_data
            })
            
            dispatch(SYSTEM_CACHE_INVALIDATION_REQUESTED, payload={"resource_type": "reward_strategy", "resource_id": strategy.id})
            
            self.logger.info(f"成功更新奖励策略 {strategy.id}")
            return self.create_success_result(strategy)
        except Exception as e:
            self.logger.exception(f"更新奖励策略失败: {str(e)}")
            await self.db.rollback()
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"更新奖励策略失败: {str(e)}"
            )
    
    async def delete_strategy(self, strategy_id: int) -> Result[RewardStrategyResponse]:
        """
        删除奖励策略
        
        Args:
            strategy_id: 策略ID
            
        Returns:
            Result[RewardStrategyResponse]: 结果对象，表示操作是否成功
        """
        self.logger.info(f"删除奖励策略 {strategy_id}")
        
        try:
            strategy = await self.get_strategy(strategy_id)
            if not strategy:
                self.logger.warning(f"奖励策略 {strategy_id} 不存在")
                return self.resource_not_found_result(strategy_id)
            
            campaign_id = strategy.campaign_id
            
            # 删除策略
            await strategy.delete(self.db)
            
            # 发送事件
            event_data = {
                "strategy_id": strategy_id,
                "strategy_name": strategy.name, # 记录名称以供参考
            }
            dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
                 "user_id": event_data.get("deleted_by"), # Assuming deleted_by in event_data or context
                 "action": "reward_strategy_deleted",
                 "resource_type": "reward_strategy",
                 "resource_id": strategy_id,
                 "metadata": event_data
            })
            
            dispatch(SYSTEM_CACHE_INVALIDATION_REQUESTED, payload={"resource_type": "reward_strategy", "resource_id": strategy_id})
            
            self.logger.info(f"成功删除奖励策略 {strategy_id}")
            return self.create_success_result({"id": strategy_id, "deleted": True})
        except Exception as e:
            self.logger.exception(f"删除奖励策略失败: {str(e)}")
            await self.db.rollback()
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"删除奖励策略失败: {str(e)}"
            )

class RewardRecordService(BaseService, ErrorResultMixin, BatchOperationMixin, CacheMixin):
    """
    奖励记录服务，负责计算、发放和管理奖励记录
    """
    resource_type = "奖励记录"

    def __init__(
        self,
        user_service: Optional[UserService] = None,
        strategy_service: Optional[RewardStrategyService] = None,
        campaign_service: Optional[CampaignService] = None,
        redis: Optional[Redis] = None,
        reward_record_repo: Optional[RewardRecordRepository] = None,
        invitation_repo: Optional[InvitationRepository] = None
    ):
        """初始化奖励记录服务

        Args:
            user_service: 用户服务实例（可选）
            strategy_service: 奖励策略服务实例（可选）
            campaign_service: 活动服务实例（可选）
            redis: Redis客户端（可选）
            reward_record_repo: 奖励记录仓库实例（可选）
            invitation_repo: 邀请仓库实例（可选）
        """
        BaseService.__init__(self, redis)
        self.user_service = user_service
        self.strategy_service = strategy_service
        self.campaign_service = campaign_service
        self.redis = redis
        self.reward_record_repo = reward_record_repo
        self.invitation_repo = invitation_repo

    
    async def _cache_record(self, record: RewardRecord) -> None:
        """
        缓存奖励记录
        
        Args:
            record: 需要缓存的奖励记录对象
        """
        if not self.redis:
            return
            
        try:
            cache_key = f"reward_record:{record.id}"
            record_data = record.to_dict()
            await self.redis.set(
                cache_key, 
                json.dumps(record_data), 
                ex=CACHE_TTL
            )
            self.logger.debug(f"奖励记录已缓存: {cache_key}")
        except Exception as e:
            self.logger.error(f"缓存奖励记录失败: {str(e)}")
            # 继续执行，不影响主流程

    async def _get_cached_record(self, record_id: int) -> Optional[RewardRecord]:
        """
        从缓存获取奖励记录
        
        Args:
            record_id: 奖励记录ID
            
        Returns:
            Optional[RewardRecord]: 缓存的奖励记录对象，如果不存在则返回None
        """
        if not self.redis:
            return None
            
        try:
            cache_key = f"reward_record:{record_id}"
            cached_data = await self.redis.get(cache_key)
            
            if cached_data:
                self.logger.debug(f"从缓存获取奖励记录: {cache_key}")
                record_data = json.loads(cached_data)
                return RewardRecord.from_dict(record_data)
        except Exception as e:
            self.logger.error(f"从缓存获取奖励记录失败: {str(e)}")
            
        return None
    
    async def get_resource_by_id(self, record_id: int) -> Optional[RewardRecord]:
        """
        获取指定ID的奖励记录

        Args:
            record_id: 记录ID

        Returns:
            Optional[RewardRecord]: 记录对象，不存在时返回None
        """
        # 先尝试从缓存获取
        cached_record = await self._get_cached_record(record_id)
        if cached_record:
            return cached_record

        # 缓存未命中，从数据库获取
        record = await self.reward_record_repo.get_by_id(record_id)

        # 如果找到记录，则缓存它
        if record:
            await self._cache_record(record)

        return record

    async def _get_applicable_strategies(
        self,
        campaign_id: int,
        is_inviter: bool
    ) -> Result[List[RewardStrategy]]:
        """获取适用于目标用户的奖励策略列表。"""
        strategies_result = await self.strategy_service.get_campaign_strategies(
            campaign_id,
            for_inviter=is_inviter,
            for_invitee=not is_inviter
        )
        if not strategies_result.is_success:
            self.logger.error(
                f"获取{'邀请人' if is_inviter else '被邀请人'}策略失败: "
                f"campaign_id={campaign_id}, error={strategies_result.result_msg or '未知错误'}",
            )
            return self.create_error_result(
                error_code=strategies_result.error_code or ErrorCode.OPERATION_FAILED,
                error_message=f"获取{'邀请人' if is_inviter else '被邀请人'}奖励策略失败: {strategies_result.error_message}"
            )

        strategy_responses = strategies_result.data.get("items", [])
        strategy_models = []
        for strategy_response in strategy_responses:
            strategy_model = await self.strategy_service.get_resource_by_id(strategy_response.id)
            if not strategy_model:
                self.logger.warning(f"无法找到 ID 为 {strategy_response.id} 的奖励策略模型，跳过此策略。")
                continue
            strategy_models.append(strategy_model)

        return self.create_success_result(strategy_models)

    async def _check_reward_eligibility(
        self,
        strategy: RewardStrategy,
        target_user_id: int,
        related_count: int
    ) -> bool:
        """检查用户是否有资格获得特定策略的奖励。"""
        # 检查最小邀请数
        if strategy.min_invitations and related_count < strategy.min_invitations:
            self.logger.debug(f"用户 {target_user_id} 未达到策略 {strategy.id} 的最小邀请数 ({strategy.min_invitations})，需要 {related_count}。")
            return False

        # 检查最大奖励次数限制
        if strategy.max_rewards:
            existing_rewards_count = await self.reward_record_repo.count_user_rewards(
                target_user_id, strategy.id
            )
            if existing_rewards_count >= strategy.max_rewards:
                self.logger.info(f"用户 {target_user_id} 对策略 {strategy.id} 的奖励已达上限 ({strategy.max_rewards})，跳过。")
                return False

        return True

    async def _calculate_reward_amount(
        self,
        strategy: RewardStrategy,
        invitation: Invitation,
        related_count: int
    ) -> Optional[float]:
        """计算单个策略的奖励金额。"""
        try:
            # 确保 RewardStrategy 模型有 calculate_reward 方法
            if not hasattr(strategy, 'calculate_reward') or not callable(strategy.calculate_reward):
                self.logger.error(f"RewardStrategy 模型 {strategy.id} 缺少 calculate_reward 方法，跳过。")
                return None

            # 调用策略的计算方法
            reward_amount = strategy.calculate_reward(
                invitation_count=related_count,
            )
            return reward_amount
        except Exception as e:
            self.logger.error(
                f"计算策略 {strategy.id} 奖励失败: user_id={invitation.inviter_id if hasattr(invitation, 'inviter_id') else 'N/A'}, "
                f"invitation_id={invitation.id}, error={str(e)}", exc_info=True
            )
            return None

    async def _create_reward_record(
        self,
        target_user_id: int,
        strategy: RewardStrategy,
        invitation: Invitation,
        reward_amount: float
    ) -> Optional[RewardRecord]:
        """创建奖励记录。"""
        try:
            reward_data = RewardRecordCreate(
                user_id=target_user_id,
                campaign_id=strategy.campaign_id,
                strategy_id=strategy.id,
                invitation_id=invitation.id,
                reward_type=strategy.reward_type,
                reward_value=reward_amount,
                is_issued=True, # 初始状态
                issued_at=get_utc_now_without_tzinfo(),
                reward_description=strategy.description
            )
            created_record = await self.reward_record_repo.create_reward(reward_data.model_dump())
            # 触发奖励发放事件 (本地或远程)
            dispatch(
                MARKETING_REWARD_ISSUED, # 使用常量
                payload={
                    "reward_record_id": created_record.id,
                    "user_id": target_user_id,
                    "campaign_id": strategy.campaign_id,
                    "strategy_id": strategy.id,
                    "invitation_id": invitation.id,
                    "amount": reward_amount,
                    "status": created_record.is_issued,
                    "description": created_record.reward_description,
                }
            )
            self.logger.info(f"成功创建奖励记录: id={created_record.id}, user={target_user_id}, strategy={strategy.id}, amount={reward_amount}")
            return created_record
        except Exception as e:
            self.logger.error(
                f"创建奖励记录失败: user_id={target_user_id}, strategy_id={strategy.id}, "
                f"invitation_id={invitation.id}, error={str(e)}", exc_info=True
            )
            # Consider rollback or specific error handling if needed
            return None

    async def _calculate_and_create_rewards_for_target(
        self,
        target_user_id: int,
        campaign: Campaign,
        invitation: Invitation,
        related_count: int,
        is_inviter: bool
    ) -> Result[List[RewardRecord]]:
        """
        计算并创建目标用户的奖励记录 (邀请人或被邀请人) - 重构版

        Args:
            target_user_id: 目标用户ID
            campaign: 活动对象
            invitation: 邀请对象
            related_count: 相关计数 (邀请人的邀请数 或 被邀请人的1)
            is_inviter: 标记是否为邀请人

        Returns:
            Result[List[RewardRecord]]: 包含成功创建的奖励记录列表的结果对象
        """
        created_records = []

        # 1. 获取适用的策略
        strategies_result = await self._get_applicable_strategies(campaign.id, is_inviter)
        if not strategies_result.is_success:
            return strategies_result # 直接返回错误结果

        strategies = strategies_result.data

        # 2. 遍历策略并计算/创建奖励
        for strategy in strategies:
            # 检查资格
            is_eligible = await self._check_reward_eligibility(strategy, target_user_id, related_count)
            if not is_eligible:
                continue

            # 计算奖励金额
            reward_amount = await self._calculate_reward_amount(strategy, invitation, related_count)
            if reward_amount is None or reward_amount <= 0:
                self.logger.info(f"策略 {strategy.id} 计算奖励为 0 或无效，跳过创建记录。")
                continue

            # 创建奖励记录
            created_record = await self._create_reward_record(
                target_user_id, strategy, invitation, reward_amount
            )
            if created_record:
                created_records.append(created_record)
            # else: # Optionally handle creation failure more explicitly
                # self.logger.error(f"未能为用户 {target_user_id} 创建策略 {strategy.id} 的奖励记录。")

        if not created_records:
             self.logger.info(f"没有为用户 {target_user_id} (campaign: {campaign.id}, invitation: {invitation.id}) 创建任何奖励记录。")
             # Return success with empty list if no rewards were applicable/created successfully
             return self.create_success_result([])

        return self.create_success_result(created_records)


    async def process_invitation_rewards(self, invitation_id: int) -> Result[Dict[str, List[int]]]:
        """
        处理单个邀请完成事件，为邀请人和被邀请人发放奖励。

        Args:
            invitation_id: 已完成的邀请ID

        Returns:
            Result[Dict[str, List[int]]]: 操作结果，data 包含 'reward_record_ids'
        """
        self.logger.info(f"开始处理邀请奖励: invitation_id={invitation_id}")
        all_created_record_ids = []

        try:
            # 1. 获取邀请信息
            invitation = await self.invitation_repo.get_by_id(invitation_id)
            if not invitation:
                self.logger.error(f"处理奖励失败: 邀请 {invitation_id} 不存在")
                return self.resource_not_found_result(invitation_id, resource_type="Invitation")

            # 检查邀请状态 (可选，取决于业务逻辑，可能已在事件触发前检查)
            # if invitation.status != 'completed': # Assuming 'completed' status exists
            #     self.logger.warning(f"邀请 {invitation_id} 状态不是 'completed'，不发放奖励。")
            #     return self.create_error_result(ErrorCode.VALIDATION_ERROR, "邀请未完成")

            inviter_id = invitation.inviter_id
            invitee_id = invitation.invitee_id
            campaign_id = invitation.campaign_id

            if not inviter_id or not invitee_id or not campaign_id:
                 self.logger.error(f"邀请 {invitation_id} 信息不完整 (inviter, invitee, or campaign missing)，无法处理奖励。")
                 return self.create_error_result(ErrorCode.VALIDATION_ERROR, "邀请信息不完整")

            # 2. 获取活动信息
            campaign_result = await self.campaign_service.get_campaign(campaign_id)
            if not campaign_result.is_success or not campaign_result.data:
                self.logger.error(f"处理奖励失败: 无法获取活动 {campaign_id} 信息")
                return self.create_error_result(
                    campaign_result.error_code or ErrorCode.CAMPAIGN_NOT_FOUND,
                    f"无法获取活动信息: {campaign_result.error_message}"
                )
            campaign = campaign_result.data # Assuming data is the Campaign model or dict
            # Convert dict to model if necessary
            if isinstance(campaign, dict):
                try:
                    campaign = Campaign.model_validate(campaign)
                except Exception as e:
                    self.logger.error(f"无法将活动数据转换为 Campaign 模型: {e}")
                    return self.create_error_result(ErrorCode.INTERNAL_ERROR, "活动数据格式错误")

            # 3. 计算邀请人相关计数 (例如，总邀请成功数)
            #    这里需要根据具体业务逻辑确定 'related_count' 的含义
            #    假设是邀请人在此活动中的总成功邀请数 (包括本次)
            inviter_success_count = await self.invitation_repo.count_successful_invitations(
                 inviter_id, campaign_id
            )

            # 4. 为邀请人计算和创建奖励
            self.logger.info(f"为邀请人 {inviter_id} 计算奖励 (invitation: {invitation_id}, count: {inviter_success_count})")
            inviter_rewards_result = await self._calculate_and_create_rewards_for_target(
                target_user_id=inviter_id,
                campaign=campaign,
                invitation=invitation,
                related_count=inviter_success_count,
                is_inviter=True
            )
            if inviter_rewards_result.is_success:
                all_created_record_ids.extend([r.id for r in inviter_rewards_result.data])
            else:
                # Log error but potentially continue for invitee
                self.logger.error(f"为邀请人 {inviter_id} 创建奖励失败: {inviter_rewards_result.error_message}")
                # Depending on requirements, might return error here
                # return inviter_rewards_result

            # 5. 为被邀请人计算和创建奖励
            #    被邀请人的相关计数通常是 1 (代表本次被邀请成功)
            self.logger.info(f"为被邀请人 {invitee_id} 计算奖励 (invitation: {invitation_id}, count: 1)")
            invitee_rewards_result = await self._calculate_and_create_rewards_for_target(
                target_user_id=invitee_id,
                campaign=campaign,
                invitation=invitation,
                related_count=1, 
                is_inviter=False
            )
            if invitee_rewards_result.is_success:
                all_created_record_ids.extend([r.id for r in invitee_rewards_result.data])
            else:
                # Log error
                self.logger.error(f"为被邀请人 {invitee_id} 创建奖励失败: {invitee_rewards_result.error_message}")
                # If inviter rewards succeeded, maybe return partial success or log only
                # If strict atomicity is needed, consider returning error

            # 6. 提交事务 (如果使用外部事务管理，则不需要在这里提交)
            # await self.reward_record_repo.db.commit() # Assuming session is managed externally or via Depends

            self.logger.info(f"邀请奖励处理完成: invitation_id={invitation_id}, created_records={all_created_record_ids}")
            return self.create_success_result({"reward_record_ids": all_created_record_ids})

        except Exception as e:
            self.logger.exception(f"处理邀请奖励时发生意外错误: invitation_id={invitation_id}, error={str(e)}")
            # await self.db.rollback() # Rollback if managing session here
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"处理奖励时发生内部错误: {str(e)}"
            )



    async def issue_reward(self, record_id: int) -> Result[RewardRecordResponse]:
        """
        发放奖励
        
        Args:
            record_id: 奖励记录ID
            
        Returns:
            Result[RewardRecordResponse]: 结果对象，包含发放状态
        """
        try:
            record = await self.get_resource_by_id(record_id)
            if not record:
                return self.resource_not_found_result(record_id)
                
            if record.is_issued:
                return self.create_error_result(
                    error_code=ErrorCode.REWARD_ALREADY_ISSUED,
                    error_message="奖励已发放"
                )
            
            # 模拟发放奖励的过程
            # 在实际应用中，这里可能会调用支付服务或积分服务等
            record = await record.update(
                
                is_issued=True,
                issued_at=get_utc_now_without_tzinfo()
            )
            
            # 发送事件
            event_data = {
                "record_id": record.id,
                "user_id": record.user_id,
                "campaign_id": record.campaign_id,
                "reward_value": record.reward_value,
                "reward_type": record.reward_type,
            }
            dispatch(MARKETING_REWARD_ISSUED, payload=event_data)
            
            self.logger.info(f"奖励记录已发放: {record_id}")
            return self.create_success_result({
                "record_id": record.id,
                "issued": True,
                "issued_at": record.issued_at
            })
        except Exception as e:
            self.logger.exception(f"发放奖励失败: {str(e)}")
            await self.db.rollback()
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"发放奖励失败: {str(e)}"
            )
    
    async def get_reward_records(self, params: GetRewardRecordsParams) -> Result[RewardRecordListResponse]:
        """获取奖励记录列表（通用）

        Args:
            params: 查询参数对象，包含分页和过滤条件

        Returns:
            Result[RewardRecordListResponse]: 包含分页信息的奖励记录列表结果
        """
        try:
            page_num = params.page_num
            page_size = params.page_size

            self.logger.info(f"获取奖励记录列表: page={page_num}, size={page_size}, filters={params.model_dump(exclude={'page_num', 'page_size'})}")

            # Assuming repository has a method get_reward_records accepting these filters
            records, total = await self.reward_record_repo.get_reward_records(
                page=page_num,
                page_size=page_size,
                campaign_id=params.campaign_id,
                user_id=params.user_id,
                reward_type=params.reward_type,
                is_issued=params.is_issued,
                strategy_id=params.strategy_id
            )

            record_responses = [RewardRecordResponse.model_validate(rec) for rec in records]

            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0

            paginated_response = RewardRecordListResponse(
                items=record_responses,
                total=total,
                page_num=page_num,
                page_size=page_size,
                page_count=total_pages
            )

            return self.create_success_result(paginated_response)
        except Exception as e:
            self.logger.error(f"获取奖励记录列表失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取奖励记录列表失败: {str(e)}"
            )

    async def get_user_rewards(
        self, 
        user_id: int,
        campaign_id: Optional[int] = None,
        is_issued: Optional[bool] = None,
        page_num: int = 1,
        page_size: int = 20
    ) -> Result:
        """
        获取用户的奖励记录
        
        Args:
            user_id: 用户ID
            campaign_id: 活动ID（可选）
            is_issued: 是否已发放（可选）
            page_num: 页码
            page_size: 每页数量
            
        Returns:
            Result[RewardRecordListResponse]: 包含奖励记录列表和分页信息的响应结果
        """
        try:
            self.logger.info(f"获取用户 {user_id} 的奖励记录: page={page_num}, size={page_size}, campaign_id={campaign_id}, is_issued={is_issued}")
            
            records, total = await self.reward_record_repo.get_user_rewards(
                
                user_id=user_id,
                campaign_id=campaign_id,
                is_issued=is_issued,
                page_num=page_num,
                page_size=page_size
            )
            
            # 构建响应列表
            record_responses = [RewardRecordResponse.model_validate(record.to_dict()) for record in records]
            
            # 计算总页数
            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0
            
            # 构建分页响应
            paginated_response = RewardRecordListResponse(
                items=record_responses,
                total=total,
                page_num=page_num,
                page_size=page_size,
                page_count=total_pages
            )
            
            return self.create_success_result(paginated_response)
        except Exception as e:
            self.logger.exception(f"获取用户 {user_id} 的奖励记录失败: {str(e)}")
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取用户奖励记录失败: {str(e)}"
            )
    
    async def get_reward_stats(
        self, 
        user_id: int,
        campaign_id: Optional[int] = None
    ) -> Result:
        """
        获取用户的奖励统计信息
        
        Args:
            user_id: 用户ID
            campaign_id: 活动ID（可选）
            
        Returns:
            Result[RewardRecordResponse]: 结果对象，包含奖励统计信息
        """
        try:
            # 获取用户奖励记录列表(不分页，获取所有)
            filters = {"user_id": user_id}
            if campaign_id is not None:
                filters["campaign_id"] = campaign_id
                
            records = await self.reward_record_repo.get_list(
                
                skip=0,
                limit=10000,  # 设置较大的限制，实际项目中可能需要分批处理
                **filters
            )
            
            # 计算总记录数
            total_count = len(records)
            
            # 计算已发放和未发放记录数
            issued_count = sum(1 for record in records if record.is_issued)
            pending_count = total_count - issued_count
            
            # 计算总奖励值
            total_value = sum(record.reward_value for record in records)
            
            # 计算已发放和未发放奖励值
            issued_value = sum(record.reward_value for record in records if record.is_issued)
            pending_value = total_value - issued_value
            
            # 按奖励类型分组统计
            by_type = {}
            for record in records:
                reward_type = record.reward_type
                if reward_type not in by_type:
                    by_type[reward_type] = {"count": 0, "value": 0}
                
                by_type[reward_type]["count"] += 1
                by_type[reward_type]["value"] += record.reward_value
            
            self.logger.info(f"用户 {user_id} 的奖励统计: 总数={total_count}, 总值={total_value}, 已发放={issued_count}(值={issued_value}), 未发放={pending_count}(值={pending_value})")
            
            return self.create_success_result(RewardStatsResponse(
                total_rewards=total_count,
                total_value=total_value,
                issued_count=issued_count,
                pending_count=pending_count,
                by_type=by_type
            ))
        except Exception as e:
            self.logger.exception(f"获取用户奖励统计失败: {str(e)}")
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取用户奖励统计失败: {str(e)}"
            )


