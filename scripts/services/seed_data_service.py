"""
种子数据服务类
提供高效的批量数据创建功能，基于现有的仓库模式和服务架构
"""
import logging
import random
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.albums.repositories.album import AlbumRepository
from svc.apps.albums.repositories.album_image import AlbumImageRepository
from svc.apps.albums.schemas.album import AlbumCreate
from svc.apps.albums.schemas.album_image import AlbumImageCreate
from svc.apps.auth.models import Permission, Role, User
from svc.apps.auth.repositories import RoleRepository, UserRepository
from svc.apps.products.repositories.category import CategoryRepository
from svc.apps.products.repositories.inventory import InventoryRepository
from svc.apps.products.repositories.product import ProductRepository
from svc.apps.shops.repositories.shop import ShopRepository
from svc.core.services.base import BaseService
from svc.core.services.cache_mixin import CacheMixin
from svc.core.services.error_result_mixin import ErrorResultMixin
from svc.core.services.batch_operation_mixin import BatchOperationMixin
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

logger = logging.getLogger(__name__)


class SeedDataService(BaseService, CacheMixin, ErrorResultMixin, BatchOperationMixin):
    """种子数据服务，提供高效的批量数据创建功能"""
    
    def __init__(self, db: AsyncSession):
        super().__init__()
        self.db = db
        self._repositories = {}
        
    def _get_repository(self, repo_class):
        """获取或创建仓库实例，实现仓库复用"""
        repo_name = repo_class.__name__
        if repo_name not in self._repositories:
            self._repositories[repo_name] = repo_class(self.db)
        return self._repositories[repo_name]
    
    async def bulk_create_permissions(self, permissions_data: List[Dict[str, Any]]) -> List[Permission]:
        """批量创建权限"""
        logger.info(f"批量创建 {len(permissions_data)} 个权限...")

        from sqlalchemy import select

        # 检查是否已存在
        result = await self.db.execute(select(Permission).limit(1))
        existing = result.scalars().first()
        if existing:
            logger.info("权限数据已存在，跳过创建")
            # 返回所有现有权限
            result = await self.db.execute(select(Permission))
            return result.scalars().all()

        # 创建临时仓库类用于批量操作
        class PermissionRepository:
            def __init__(self, db):
                self.db = db
                self.model = Permission

            async def bulk_create(self, data_list, **kwargs):
                from svc.core.repositories.base import BaseRepository
                base_repo = BaseRepository(self.db, self.model)
                return await base_repo.bulk_create(data_list, **kwargs)

        # 使用批量创建
        repo = PermissionRepository(self.db)
        created_count = await repo.bulk_create(permissions_data)

        # 返回创建的权限
        result = await self.db.execute(select(Permission).order_by(Permission.id))
        permissions = result.scalars().all()

        logger.info(f"成功批量创建 {created_count} 个权限")
        return permissions
    
    async def bulk_create_roles(self, roles_data: List[Dict[str, Any]], permissions: List[Permission]) -> List[Role]:
        """批量创建角色"""
        logger.info(f"批量创建 {len(roles_data)} 个角色...")
        
        # 检查是否已存在
        role_repo = self._get_repository(RoleRepository)
        existing_roles = await role_repo.get_list(limit=10)
        if existing_roles:
            logger.info("角色数据已存在，跳过创建")
            return existing_roles
        
        # 创建权限名称到权限对象的映射
        permission_map = {perm.name: perm for perm in permissions}

        # 创建角色（需要处理权限关系，不能用纯bulk insert）
        created_roles = []
        for role_data in roles_data:
            role_permission_names = role_data.pop("permissions", [])
            role = await role_repo.create(**role_data)

            # 将权限名称转换为权限对象
            role_permission_objects = []
            for perm_name in role_permission_names:
                if perm_name in permission_map:
                    role_permission_objects.append(permission_map[perm_name])
                else:
                    logger.warning(f"权限 '{perm_name}' 不存在，跳过")

            role.permissions = role_permission_objects
            created_roles.append(role)
        
        await self.db.commit()
        logger.info(f"成功批量创建 {len(created_roles)} 个角色")
        return created_roles
    
    async def bulk_create_categories(self, categories_data: List[Dict[str, Any]]) -> List:
        """批量创建分类"""
        logger.info(f"批量创建 {len(categories_data)} 个分类...")
        
        category_repo = self._get_repository(CategoryRepository)
        
        # 检查已存在的分类
        existing_categories, _ = await category_repo.get_categories()
        existing_names = {cat.name for cat in existing_categories}
        
        # 过滤出需要创建的分类
        to_create = [data for data in categories_data if data['name'] not in existing_names]
        
        if not to_create:
            logger.info("所有分类已存在，跳过创建")
            return existing_categories
        
        # 使用批量创建
        created_count = await category_repo.bulk_create(to_create)
        logger.info(f"成功批量创建 {created_count} 个分类")
        
        # 返回所有分类
        all_categories, _ = await category_repo.get_categories()
        return all_categories
    
    async def bulk_create_products_with_albums(
        self, 
        products_data: List[Dict[str, Any]], 
        albums_data: List[Dict[str, Any]],
        images_data: List[Dict[str, Any]]
    ) -> Tuple[List, List, List]:
        """批量创建产品及其关联的图册和图片"""
        logger.info(f"批量创建 {len(products_data)} 个产品及其图册...")
        
        product_repo = self._get_repository(ProductRepository)
        album_repo = self._get_repository(AlbumRepository)
        album_image_repo = self._get_repository(AlbumImageRepository)
        
        # 检查是否已存在产品
        existing_products, total = await product_repo.get_products()
        if total > 0:
            logger.info("产品数据已存在，跳过创建")
            return existing_products, [], []
        
        # 批量创建图册
        created_albums_count = await album_repo.bulk_create(albums_data)
        logger.info(f"批量创建了 {created_albums_count} 个图册")
        
        # 获取创建的图册以获取ID
        albums, _ = await album_repo.get_albums()
        album_id_map = {album.name: album.id for album in albums}
        
        # 更新图片数据中的album_id
        valid_images_data = []
        for img_data in images_data:
            album_name = img_data.pop('album_name', None)
            if album_name and album_name in album_id_map:
                img_data['album_id'] = album_id_map[album_name]
                valid_images_data.append(img_data)
            else:
                logger.warning(f"跳过图片，找不到对应的图册: {album_name}")

        images_data = valid_images_data
        
        # 批量创建图片
        created_images_count = await album_image_repo.bulk_create(images_data)
        logger.info(f"批量创建了 {created_images_count} 个图片")
        
        # 批量创建产品（需要关联album_id）
        for i, product_data in enumerate(products_data):
            if i < len(albums):
                product_data['album_id'] = albums[i].id
        
        created_products_count = await product_repo.bulk_create(products_data)
        logger.info(f"批量创建了 {created_products_count} 个产品")
        
        # 返回创建的数据
        products, _ = await product_repo.get_products()
        images, _ = await album_image_repo.get_album_images()
        
        return products, albums, images
    
    async def bulk_create_shops_with_albums(
        self,
        shops_data: List[Dict[str, Any]],
        albums_data: List[Dict[str, Any]],
        images_data: List[Dict[str, Any]]
    ) -> Tuple[List, List, List]:
        """批量创建门店及其关联的图册和图片"""
        logger.info(f"批量创建 {len(shops_data)} 个门店及其图册...")
        
        shop_repo = self._get_repository(ShopRepository)
        album_repo = self._get_repository(AlbumRepository)
        album_image_repo = self._get_repository(AlbumImageRepository)
        
        # 检查是否已存在门店
        from svc.apps.shops.schemas.shop import GetShopsParams
        params = GetShopsParams(page_num=1, page_size=1)
        existing_shops, total = await shop_repo.get_shops(params)
        if total >= len(shops_data):
            logger.info("门店数据已存在，跳过创建")
            return existing_shops, [], []
        
        # 批量创建门店图册
        created_albums_count = await album_repo.bulk_create(albums_data)
        logger.info(f"批量创建了 {created_albums_count} 个门店图册")
        
        # 获取创建的图册
        all_albums, _ = await album_repo.get_albums()
        shop_albums = [album for album in all_albums if "门店" in album.name]
        
        # 更新门店数据中的album_id
        for i, shop_data in enumerate(shops_data):
            if i < len(shop_albums):
                shop_data['album_id'] = shop_albums[i].id
        
        # 批量创建门店
        created_shops_count = await shop_repo.bulk_create(shops_data)
        logger.info(f"批量创建了 {created_shops_count} 个门店")
        
        # 批量创建门店图片
        created_images_count = await album_image_repo.bulk_create(images_data)
        logger.info(f"批量创建了 {created_images_count} 个门店图片")
        
        # 返回创建的数据
        all_shops, _ = await shop_repo.get_shops(GetShopsParams(page_num=1, page_size=100))
        all_images, _ = await album_image_repo.get_album_images()
        
        return all_shops, shop_albums, all_images

    async def prepare_products_data(self, categories: List) -> Tuple[List[Dict], List[Dict], List[Dict]]:
        """准备产品、图册和图片数据"""
        products_data = []
        albums_data = []
        images_data = []

        # PNG透明底图片库
        png_image_urls = [
            "http://res.yqbaijiu.com/20250626/dc43232763b24acf8012fdbe392277d0.jpg",
            "http://res.yqbaijiu.com/20250626/80953d93b119496a9b70684ed12c9732.jpg",
            "http://res.yqbaijiu.com/20250626/693fa52621204022b05b7e92baeba619.jpg",
            "http://res.yqbaijiu.com/20250626/dce92a01ccf64421939b9c7ae8572e69.jpg",
            "http://res.yqbaijiu.com/20250626/5cfbb8b5a67647c8a8b2231fe3a13bdc.jpg",
            "http://res.yqbaijiu.com/20250626/06f1f9db12ff47a9837b2ad089f63a3f.jpg",
            "http://res.yqbaijiu.com/20250626/ac326a6f919a40788e10328d07048ea5.jpg",
            "http://res.yqbaijiu.com/20250626/35d60a1e25744865adac3f179e7a1cd1.jpg",
            "http://res.yqbaijiu.com/20250626/90d9d57057f54dcea3898f0b2984b0fe.jpg",
        ]

        for idx, cat in enumerate(categories):
            helmet_type = cat.name
            for i in range(5):
                product_name = f"{helmet_type}{i+1}"
                sku = f"{helmet_type[:2].upper()}{i+1:02d}"

                # 准备图册数据
                album_data = {
                    "name": f"{product_name}图册",
                    "description": f"{product_name}的主图册",
                    "tags": [helmet_type, "头盔"],
                    "status": "active",
                    "sort_order": idx*5+i+1,
                    "meta_data": {"scene": "product"}
                }
                albums_data.append(album_data)

                # 准备图片数据
                num_images = random.randint(1, 3)
                for j in range(num_images):
                    url = random.choice(png_image_urls)
                    image_data = {
                        "album_name": f"{product_name}图册",  # 临时字段，后续会转换为album_id
                        "url": url,
                        "file_name": url.split("/")[-1],
                        "file_size": 0,
                        "width": 800,
                        "height": 800,
                        "mime_type": "image/png",
                        "is_cover": (j == 0),
                        "sort_order": j+1,
                        "status": "active",
                        "meta_data": {"scene": "product"}
                    }
                    images_data.append(image_data)

                # 准备产品数据
                product_data = {
                    "name": product_name,
                    "description": f"{helmet_type}高品质防护，适合多场景使用。",
                    "short_description": f"优质{helmet_type}，安全舒适。",
                    "sku": sku,
                    "barcode": f"{sku}BAR{i+1:03d}",
                    "category_id": cat.id,
                    "price": 199.0 + idx*20 + i*5,
                    "cost_price": 120.0 + idx*10,
                    "market_price": 299.0 + idx*30,
                    "currency": "CNY",
                    "is_featured": (i == 0),
                    "is_digital": False,
                    "track_inventory": True,
                    "stock_quantity": 50 + i*10,
                    "min_stock_level": 5,
                    "max_stock_level": 200,
                    "weight": 0.45 + idx*0.05,
                    "dimensions": {"length": 25+idx, "width": 20+idx, "height": 15+idx},
                    "attributes": {"类型": helmet_type},
                    "seo_title": f"{product_name} - 专业{helmet_type}商城",
                    "seo_description": f"{helmet_type}，安全舒适，适合骑行、施工等多场景。",
                    "seo_keywords": f"{helmet_type},头盔,安全,防护",
                    "rich_description": f'<p>{helmet_type}，高品质防护，适合多场景使用。</p>'
                }
                products_data.append(product_data)

        return products_data, albums_data, images_data

    async def prepare_shops_data(self) -> Tuple[List[Dict], List[Dict], List[Dict]]:
        """准备门店、图册和图片数据"""
        shops_data = []
        albums_data = []
        images_data = []

        shop_names = [f"测试门店{i+1}" for i in range(10)]

        for idx, name in enumerate(shop_names):
            # 准备门店数据
            shop_data = {
                "name": name,
                "description": f"{name}，优质服务，欢迎光临！",
                "address_line1": f"测试路{100+idx}号",
                "address_line2": None,
                "city": "测试市",
                "state_province": "测试省",
                "postal_code": f"1000{idx}",
                "country": "CN",
                "phone_number": f"010-8888{1000+idx}",
                "email": f"shop{idx+1}@example.com",
                "website": f"https://shop.example.com/{idx+1}",
                "opening_hours": {"Mon-Sun": "9am-9pm"},
                "is_franchise": bool(idx % 2),
                "latitude": str(30.0 + idx * 0.1),
                "longitude": str(120.0 + idx * 0.1),
                "extra_info": {"floor_area_sqm": 100 + idx * 10, "has_parking": bool(idx % 2)},
                "status": "open"
            }
            shops_data.append(shop_data)

            # 准备图册数据
            album_data = {
                "name": f"{name}图册",
                "description": f"{name}的主图册",
                "tags": ["门店"],
                "status": "active",
                "sort_order": idx + 1,
                "meta_data": {"scene": "shop"}
            }
            albums_data.append(album_data)

            # 准备图片数据
            num_images = random.randint(1, 3)
            for j in range(num_images):
                url = f"https://picsum.photos/seed/{random.randint(1000,9999)}/800/800"
                image_data = {
                    "album_name": f"{name}图册",  # 临时字段
                    "url": url,
                    "file_name": f"shop_{idx+1}_img{j+1}.jpg",
                    "file_size": 0,
                    "width": 800,
                    "height": 800,
                    "mime_type": "image/jpeg",
                    "is_cover": (j == 0),
                    "sort_order": j + 1,
                    "status": "active",
                    "meta_data": {"scene": "shop"}
                }
                images_data.append(image_data)

        return shops_data, albums_data, images_data
