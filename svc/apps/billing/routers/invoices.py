"""
账单管理API路由
包含账单的创建、查询、支付和取消功能
"""
from typing import Any, List, Optional, Dict

from fastapi import APIRouter, Depends, HTTPException, Query, Path, status

from svc.core.models.result import Result
from svc.apps.auth.dependencies import get_current_active_user, get_current_superuser, has_permission, resource_permission
from svc.apps.auth.models.user import User
from svc.apps.billing.schemas import (
    InvoiceCreate,
    InvoiceResponse,
    InvoiceListResponse,
    GetInvoiceParams,
    GetInvoicesParams,
    CreateInvoiceParams,
    CancelInvoiceParams,
    MarkInvoicePaidParams,
)
from svc.apps.billing.services import InvoiceService
from svc.apps.billing.dependencies import get_invoice_service
from svc.core.exceptions.route_error_handler import handle_route_errors, INVOICE_ERROR_MAPPING
from svc.core.schemas.base import PageParams

# Define the router for this file
router = APIRouter(tags=["账单"])

# === 客户端路由 (Client Routes) ===

@router.get("/mine", response_model=Result[Dict[str, Any]])
@handle_route_errors(INVOICE_ERROR_MAPPING)
async def get_my_invoices(
    status: Optional[str] = Query(None, description="按账单状态过滤", alias="status"),
    params: PageParams = Depends(),
    invoice_service: InvoiceService = Depends(get_invoice_service),
    current_user: User = Depends(get_current_active_user),
) -> Result[Dict[str, Any]]:
    """获取当前用户的账单列表"""
    params_obj = GetInvoicesParams(
        user_id=current_user.id,
        status=status,
        page_num=params.page_num,
        page_size=params.page_size
    )
    result = await invoice_service.get_invoices(params_obj)
    return result

@router.get("/mine/unpaid", response_model=Result[Dict[str, Any]])
@handle_route_errors(INVOICE_ERROR_MAPPING)
async def get_my_unpaid_invoices(
    params: PageParams = Depends(),
    invoice_service: InvoiceService = Depends(get_invoice_service),
    current_user: User = Depends(get_current_active_user),
) -> Result[Dict[str, Any]]:
    """获取当前用户未支付的账单列表"""
    params_obj = GetInvoicesParams(
        user_id=current_user.id,
        status="unpaid",
        page_num=params.page_num,
        page_size=params.page_size
    )
    result = await invoice_service.get_invoices(params_obj)
    return result

@router.get("/details/{invoice_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors(INVOICE_ERROR_MAPPING)
async def get_my_invoice_details(
    invoice_id: int = Path(..., description="账单ID"),
    invoice_service: InvoiceService = Depends(get_invoice_service),
    current_user: User = Depends(get_current_active_user),
) -> Result[Dict[str, Any]]:
    """获取当前用户的特定账单详情"""
    params = GetInvoiceParams(invoice_id=invoice_id, user_id=current_user.id)
    result = await invoice_service.get_invoice(params)
    return result

# === 管理端路由 (Admin Routes) ===

@router.get("/admin/list", response_model=Result[Dict[str, Any]])
@handle_route_errors(INVOICE_ERROR_MAPPING)
async def admin_list_invoices(
    user_id: Optional[int] = Query(None, description="按用户ID过滤", alias="userId"),
    subscription_id: Optional[int] = Query(None, description="按订阅ID过滤", alias="subscriptionId"),
    status: Optional[str] = Query(None, description="按账单状态过滤", alias="status"),
    params: PageParams = Depends(),
    invoice_service: InvoiceService = Depends(get_invoice_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("invoice:read"))
) -> Result[Dict[str, Any]]:
    """获取账单列表 (管理端)"""
    params_obj = GetInvoicesParams(
        user_id=user_id,
        subscription_id=subscription_id,
        status=status,
        page_num=params.page_num,
        page_size=params.page_size
    )
    result = await invoice_service.get_invoices(params_obj)
    return result

@router.get("/admin/details/{invoice_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors(INVOICE_ERROR_MAPPING)
async def admin_get_invoice_details(
    invoice_id: int = Path(..., description="账单ID"),
    invoice_service: InvoiceService = Depends(get_invoice_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("invoice", "read"))
) -> Result[Dict[str, Any]]:
    """获取任意账单详情 (管理端)"""
    params = GetInvoiceParams(invoice_id=invoice_id, user_id=None)
    result = await invoice_service.get_invoice(params)
    return result

@router.post("/admin/create", response_model=Result[Dict[str, Any]])
@handle_route_errors(INVOICE_ERROR_MAPPING)
async def admin_create_invoice(
    invoice_data: InvoiceCreate,
    invoice_service: InvoiceService = Depends(get_invoice_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("invoice:create"))
) -> Result[Dict[str, Any]]:
    """手动创建账单 (管理端)"""
    params = CreateInvoiceParams(invoice_data=invoice_data)
    result = await invoice_service.create_invoice(params)
    return result

@router.post("/admin/cancel/{invoice_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors(INVOICE_ERROR_MAPPING)
async def admin_cancel_invoice(
    invoice_id: int = Path(..., description="账单ID"),
    invoice_service: InvoiceService = Depends(get_invoice_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("invoice:cancel"))
) -> Result[Dict[str, Any]]:
    """取消账单 (管理端)"""
    params = CancelInvoiceParams(invoice_id=invoice_id)
    result = await invoice_service.cancel_invoice(params)
    return result

@router.post("/admin/mark-paid/{invoice_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors(INVOICE_ERROR_MAPPING)
async def admin_mark_invoice_paid(
    invoice_id: int = Path(..., description="账单ID"),
    payment_id: Optional[int] = Query(None, description="关联支付ID", alias="paymentId"),
    invoice_service: InvoiceService = Depends(get_invoice_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("invoice:mark_paid"))
) -> Result[Dict[str, Any]]:
    """手动标记账单为已支付 (管理端)"""
    params = MarkInvoicePaidParams(
        invoice_id=invoice_id,
        payment_id=payment_id
    )
    result = await invoice_service.mark_invoice_paid(params)
    return result