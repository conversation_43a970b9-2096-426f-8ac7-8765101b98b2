"""
事务管理模块。
提供事务装饰器、超时控制和事务监控工具。
"""

import functools
import asyncio
import logging
import time
from datetime import datetime
from typing import Callable, TypeVar, Any, Optional, Dict

from sqlalchemy.ext.asyncio import AsyncSession
from contextlib import asynccontextmanager, nullcontext

from svc.core.database.session import get_session
from svc.core.config.settings import get_settings

# 配置日志
logger = logging.getLogger(__name__)

# 类型变量定义
T = TypeVar('T')
F = TypeVar('F', bound=Callable[..., Any])

# 全局事务监控
_active_transactions: Dict[str, Dict[str, Any]] = {}
_transaction_stats: Dict[str, int] = {
    "started": 0,
    "completed": 0,
    "failed": 0,
    "timeouts": 0
}

class TransactionTimeoutError(Exception):
    """事务超时异常"""
    pass

class TransactionError(Exception):
    """事务执行异常"""
    pass

@asynccontextmanager
async def timeout(seconds: int):
    """
    异步超时上下文管理器
    
    Args:
        seconds: 超时秒数
        
    Raises:
        asyncio.TimeoutError: 如果操作超时
    """
    task = asyncio.current_task()
    if task is None:
        # 如果不在事件循环中，直接yield
        yield
        return
    
    # 创建超时任务
    timer_handle = asyncio.get_event_loop().call_later(
        seconds, 
        task.cancel
    )
    
    try:
        yield
    except asyncio.CancelledError:
        # 操作被取消，很可能是因为超时
        raise asyncio.TimeoutError(f"操作超时（{seconds}秒）")
    finally:
        timer_handle.cancel()

def with_transaction_timeout(auto_commit: bool = True, timeout_seconds: Optional[int] = None):
    """
    事务装饰器，支持超时控制和自动提交
    
    Args:
        auto_commit: 是否自动提交事务
        timeout_seconds: 超时秒数，None则使用配置默认值
    Returns:
        装饰器函数
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 获取数据库会话对象
            db = kwargs.get('db')
            if db is None:
                for arg in args:
                    if isinstance(arg, AsyncSession):
                        db = arg
                        break
            
            # 确定超时时间
            settings = get_settings()
            actual_timeout = timeout_seconds or settings.db_pool_timeout
            
            # 生成事务ID
            transaction_id = f"{func.__module__}.{func.__name__}_{id(db)}_{int(time.time())}"
            
            # 记录事务开始
            start_time = datetime.now()
            _transaction_stats["started"] += 1
            _active_transactions[transaction_id] = {
                "function": f"{func.__module__}.{func.__name__}",
                "start_time": start_time,
                "timeout": actual_timeout
            }
            
            logger.debug(f"事务开始: {transaction_id}, 超时设置: {actual_timeout}秒")
            
            try:
                # 设置超时上下文
                async with timeout(actual_timeout) if actual_timeout else nullcontext():
                    result = await func(*args, **kwargs)
                    
                    # 自动提交事务
                    if auto_commit and db is not None and hasattr(db, 'commit'):
                        try:
                            await db.commit()
                        except Exception as commit_error:
                            logger.error(f"事务提交失败: {transaction_id}, 错误: {str(commit_error)}")
                            await db.rollback()
                            raise
                
                # 记录事务完成
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                logger.debug(f"事务完成: {transaction_id}, 耗时: {duration:.2f}秒")
                _transaction_stats["completed"] += 1
                
                return result
                
            except asyncio.TimeoutError:
                # 记录超时并回滚事务
                if db is not None and hasattr(db, 'rollback'):
                    await db.rollback()
                
                logger.error(f"事务超时 ({actual_timeout}秒): {transaction_id}")
                _transaction_stats["timeouts"] += 1
                
                raise TransactionTimeoutError(f"操作超时，请稍后重试")
                
            except Exception as e:
                # 记录失败并回滚事务
                if db is not None and hasattr(db, 'rollback'):
                    await db.rollback()
                
                logger.error(f"事务失败: {transaction_id}, 错误: {str(e)}", exc_info=True)
                _transaction_stats["failed"] += 1
                
                raise
                
            finally:
                # 从活跃事务中移除
                if transaction_id in _active_transactions:
                    del _active_transactions[transaction_id]
        
        return wrapper
    
    return decorator

def with_transaction(auto_commit: bool = True):
    """
    事务装饰器，自动处理会话创建、提交和回滚
    
    Args:
        auto_commit: 是否自动提交事务
        
    Returns:
        装饰器函数
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 检查是否已提供数据库会话
            existing_db = kwargs.get('db')
            if existing_db is not None:
                # 使用已有会话
                try:
                    result = await func(*args, **kwargs)
                    if auto_commit:
                        await existing_db.commit()
                    return result
                except Exception as e:
                    await existing_db.rollback()
                    logger.error(f"使用现有会话的事务操作失败: {str(e)}")
                    raise
            else:
                # 创建新会话
                async with get_session() as session:
                    try:
                        kwargs['db'] = session
                        result = await func(*args, **kwargs)
                        if auto_commit:
                            await session.commit()
                        return result
                    except Exception as e:
                        await session.rollback()
                        logger.error(f"事务操作失败: {str(e)}")
                        raise
        
        return wrapper
    
    return decorator

def get_transaction_stats() -> Dict[str, Any]:
    """
    获取事务统计信息
    
    Returns:
        Dict[str, Any]: 事务统计信息
    """
    stats = _transaction_stats.copy()
    stats["active_count"] = len(_active_transactions)
    
    # 计算长时间运行的事务数量
    long_running_threshold = get_settings().db_pool_timeout / 2
    now = datetime.now()
    long_running_count = 0
    
    for tx_info in _active_transactions.values():
        duration = (now - tx_info["start_time"]).total_seconds()
        if duration > long_running_threshold:
            long_running_count += 1
    
    stats["long_running_count"] = long_running_count
    
    return stats

def get_active_transactions(include_details: bool = False) -> Dict[str, Any]:
    """
    获取活跃事务信息
    
    Args:
        include_details: 是否包含详细信息
        
    Returns:
        Dict[str, Any]: 活跃事务信息
    """
    result = {
        "count": len(_active_transactions),
        "transactions": {}
    }
    
    if include_details:
        now = datetime.now()
        for tx_id, tx_info in _active_transactions.items():
            duration = (now - tx_info["start_time"]).total_seconds()
            tx_details = {
                "function": tx_info["function"],
                "duration": duration,
                "timeout": tx_info["timeout"],
                "start_time": tx_info["start_time"].isoformat()
            }
            result["transactions"][tx_id] = tx_details
    
    return result

async def check_long_running_transactions() -> None:
    """
    检查长时间运行的事务，并记录警告
    
    该函数可以定期调用，检测潜在的长事务问题
    """
    settings = get_settings()
    now = datetime.now()
    warning_threshold = settings.db_pool_timeout * 0.5  # 警告阈值为超时时间的50%
    
    for tx_id, tx_info in list(_active_transactions.items()):
        duration = (now - tx_info["start_time"]).total_seconds()
        if duration > warning_threshold:
            logger.warning(
                f"检测到长时间运行事务: {tx_id}, "
                f"函数: {tx_info['function']}, "
                f"已运行: {duration:.2f}秒, "
                f"超时设置: {tx_info['timeout']}秒"
            )