"""
服务层基类模块。
提供服务层抽象基类和通用功能。

本模块提供了一个服务层的抽象基类，包含错误处理、结果创建和缓存管理等通用功能。
所有的服务类都应该继承自BaseService类，并实现必要的方法。

缓存使用指南:
- 所有需要缓存功能的服务类应该在构造函数中传入redis参数
- 使用cache_resource方法缓存资源数据，可以自动处理各种类型的数据
- 使用get_cached_resource方法获取缓存的资源数据，提供反序列化函数以将JSON转回对象
- 使用delete_cache方法删除一个或多个缓存键
- 子类可以定义自己的辅助方法来生成标准化的缓存键

示例:
```python
class MyService(BaseService[MyModel, MyResult]):
    def _get_my_model_cache_key(self, model_id: int) -> str:
        return f"my_model:{model_id}"
        
    async def get_resource_by_id(self, model_id: int) -> Optional[MyModel]:
        # 先尝试从缓存获取
        cache_key = self._get_my_model_cache_key(model_id)
        cached_model = await self.get_cached_resource(
            cache_key,
            lambda data: MyModel.from_dict(data)
        )
        if cached_model:
            return cached_model
        
        # 如果找到模型，则缓存它
        if model:
            await self.cache_resource(cache_key, model)
            
        return model
```
"""
from .logger_mixin import LoggerMixin
from typing import Generic, Optional, TypeVar, Union

ModelType = TypeVar("ModelType")

class BaseService(LoggerMixin, Generic[ModelType]):
    """服务基类，仅定义基础接口，具体功能由mixin组合"""
    resource_type: str = "资源"

    def __init__(self, redis=None):
        pass

    async def warm_up_cache(self, **filters) -> None:
        """预热缓存，子类需实现"""
        raise NotImplementedError("子类需要实现此方法")

    async def get_resource_by_id(self, resource_id: Union[str, int]) -> Optional[ModelType]:
        """获取指定ID的资源，子类需实现"""
        raise NotImplementedError("子类必须实现get_resource_by_id方法")