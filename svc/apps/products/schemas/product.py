from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field

from svc.apps.albums.schemas.album import AlbumResponse, UserAlbumResponse
from svc.core.models.base import CamelCaseModel
from svc.core.schemas.base import PaginatedResponse
from svc.core.schemas.batch import BaseBatchUpdateRequest, BatchUpdateResponse


class ProductBase(CamelCaseModel):
    """商品基础模型"""
    model_config = ConfigDict(str_strip_whitespace=True)
    name: str = Field(..., description="商品名称")
    description: Optional[str] = Field(default=None, description="商品描述")
    short_description: Optional[str] = Field(default=None, description="简短描述")
    category_id: Optional[int] = Field(default=None, description="主分类ID")
    status: Optional[str] = Field(default="draft", description="商品状态")
    price: int = Field(..., description="标准售价（分）")
    currency: str = Field(default="CNY", description="币种")
    stock_quantity: int = Field(default=0, description="总库存")
    min_stock_level: int = Field(default=0, description="最低库存预警线")
    track_inventory: bool = Field(default=True, description="是否启用库存管理")
    sales_count: int = Field(default=0, description="累计销量")
    view_count: int = Field(default=0, description="累计浏览量")
    album_id: Optional[int] = Field(default=None, description="主图册ID")
    image_url: Optional[str] = Field(default=None, description="主图URL")
    slug: Optional[str] = Field(default=None, description="URL别名")
    seo_title: Optional[str] = Field(default=None, description="SEO标题")
    seo_keywords: Optional[str] = Field(default=None, description="SEO关键词")
    seo_description: Optional[str] = Field(default=None, description="SEO描述")
    is_featured: bool = Field(default=False, description="是否推荐/置顶")
    attributes: Dict[str, Any] = Field(default_factory=dict, description="自定义属性")
    rich_description: Optional[str] = Field(default=None, description="富文本详情")
    sort_order: int = Field(default=0, description="排序权重")
    meta_data: Dict[str, Any] = Field(default_factory=dict, description="元数据")

class ProductCreate(ProductBase):
    """商品创建模型"""
    pass

class ProductUpdate(CamelCaseModel):
    """商品更新模型"""
    model_config = ConfigDict(str_strip_whitespace=True)
    name: Optional[str] = Field(default=None, description="商品名称")
    description: Optional[str] = Field(default=None, description="商品描述")
    short_description: Optional[str] = Field(default=None, description="简短描述")
    category_id: Optional[int] = Field(default=None, description="主分类ID")
    status: Optional[str] = Field(default=None, description="商品状态")
    price: Optional[int] = Field(default=None, description="标准售价（分）")
    currency: Optional[str] = Field(default=None, description="币种")
    stock_quantity: Optional[int] = Field(default=None, description="总库存")
    min_stock_level: Optional[int] = Field(default=None, description="最低库存预警线")
    track_inventory: Optional[bool] = Field(default=None, description="是否启用库存管理")
    sales_count: Optional[int] = Field(default=None, description="累计销量")
    view_count: Optional[int] = Field(default=None, description="累计浏览量")
    album_id: Optional[int] = Field(default=None, description="主图册ID")
    image_url: Optional[str] = Field(default=None, description="主图URL")
    slug: Optional[str] = Field(default=None, description="URL别名")
    seo_title: Optional[str] = Field(default=None, description="SEO标题")
    seo_keywords: Optional[str] = Field(default=None, description="SEO关键词")
    seo_description: Optional[str] = Field(default=None, description="SEO描述")
    is_featured: Optional[bool] = Field(default=None, description="是否推荐/置顶")
    attributes: Optional[Dict[str, Any]] = Field(default=None, description="自定义属性")
    rich_description: Optional[str] = Field(default=None, description="富文本详情")
    sort_order: Optional[int] = Field(default=None, description="排序权重")
    meta_data: Optional[Dict[str, Any]] = Field(default=None, description="元数据")

class ProductResponse(ProductBase):
    """商品响应模型"""
    model_config = ConfigDict(from_attributes=True, str_strip_whitespace=True)
    id: int = Field(..., description="商品ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    deleted_at: Optional[datetime] = Field(default=None, description="删除时间")

class ProductListResponse(PaginatedResponse[ProductResponse]):
    """商品列表响应模型"""
    pass

# SKU相关schema
class ProductSKUBase(CamelCaseModel):
    """SKU基础模型"""
    model_config = ConfigDict(str_strip_whitespace=True)
    product_id: int = Field(..., description="所属产品ID")
    sku: str = Field(..., description="SKU编码")
    price: int = Field(..., description="SKU售价（分）")
    stock_quantity: int = Field(default=0, description="SKU库存")
    status: str = Field(default="active", description="SKU状态")
    sort_order: int = Field(default=0, description="排序权重")
    image_url: Optional[str] = Field(default=None, description="SKU主图URL")
    meta_data: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    spec_option_ids: List[int] = Field(default_factory=list, description="规格选项ID列表")

class ProductSKUCreate(ProductSKUBase):
    """SKU创建模型"""
    pass

class ProductSKUUpdate(CamelCaseModel):
    """SKU更新模型"""
    model_config = ConfigDict(str_strip_whitespace=True)
    sku: Optional[str] = Field(default=None, description="SKU编码")
    price: Optional[int] = Field(default=None, description="SKU售价（分）")
    stock_quantity: Optional[int] = Field(default=None, description="SKU库存")
    status: Optional[str] = Field(default=None, description="SKU状态")
    sort_order: Optional[int] = Field(default=None, description="排序权重")
    image_url: Optional[str] = Field(default=None, description="SKU主图URL")
    meta_data: Optional[Dict[str, Any]] = Field(default=None, description="元数据")
    spec_option_ids: Optional[List[int]] = Field(default=None, description="规格选项ID列表")

class ProductSKUResponse(ProductSKUBase):
    """SKU响应模型"""
    model_config = ConfigDict(from_attributes=True, str_strip_whitespace=True)
    id: int = Field(..., description="SKU ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    deleted_at: Optional[datetime] = Field(default=None, description="删除时间")

class ProductSKUListResponse(PaginatedResponse[ProductSKUResponse]):
    """SKU列表响应模型"""
    pass

class GetProductsParams(CamelCaseModel):
    """获取商品列表的查询参数模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        extra='ignore'
    )
    
    page_num: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=1000, description="每页数量")
    status: Optional[str] = Field(default=None, description="商品状态")
    category_id: Optional[int] = Field(default=None, description="分类ID")
    is_featured: Optional[bool] = Field(default=None, description="是否为推荐商品")
    search_term: Optional[str] = Field(default=None, description="搜索关键词")
    min_price: Optional[float] = Field(default=None, description="最低价格")
    max_price: Optional[float] = Field(default=None, description="最高价格")
    order_by: Optional[str] = Field(default="created_at", description="排序字段")
    order_desc: Optional[bool] = Field(default=True, description="是否降序")

class UserProductSKU(CamelCaseModel):
    """用户端产品规格组合，仅用于用户端接口，减少冗余字段传输"""
    id: int = Field(..., description="规格组合ID")
    sku: str = Field(..., description="SKU")
    price: float = Field(..., description="价格")
    stock_quantity: int = Field(..., description="库存数量")
    spec_option_ids: List[int] = Field(..., description="该组合包含的所有规格值ID")
    spec_option_values: List[str] = Field(..., description="该组合包含的所有规格值")
    album_url: Optional[str] = Field(default=None, description="规格图片URL")

class UserProductSpec(CamelCaseModel):
    """用户端产品规格属性结构"""
    id: int = Field(..., description="规格ID")
    name: str = Field(..., description="规格名")
    options: List[dict] = Field(..., description="可选值列表，如[{id, value}]")

class UserProductResponse(CamelCaseModel):
    """用户端产品响应模型，列表用image_url，详情用album"""
    id: int
    name: str
    description: Optional[str] = None
    short_description: Optional[str] = None
    price: float
    currency: str
    image_url: Optional[str] = None  # 产品封面图片URL（列表用）
    album: Optional[UserAlbumResponse] = None  # 产品主图册（详情用，精简版）
    is_featured: bool
    attributes: Dict[str, Any]
    rich_description: Optional[str] = None
    # specs: List[UserProductSpec]
    # spec_combinations: List[UserProductSpecCombination]

class UserProductListResponse(PaginatedResponse[UserProductResponse]):
    """用户端商品分页响应模型"""
    pass

class ProductBatchUpdate(CamelCaseModel):
    """商品批量更新模型 - 只包含允许批量更新的字段"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "price": 999.00,
                "status": "active",
                "is_featured": True,
                "category_id": 2
            }
        }
    )

    price: Optional[float] = Field(default=None, description="商品价格")
    status: Optional[str] = Field(default=None, description="商品状态")
    is_featured: Optional[bool] = Field(default=None, description="是否为推荐商品")
    category_id: Optional[int] = Field(default=None, description="分类ID")

class ProductBatchUpdateRequest(BaseBatchUpdateRequest[ProductBatchUpdate]):
    """产品批量更新请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30],
                "update_data": {
                    "status": "active",
                }
            }
        }
    )
