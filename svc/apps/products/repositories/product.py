"""
商品数据访问层。
负责商品模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""

from typing import Any, Dict, List, Optional, Tuple, Union

from sqlalchemy import and_, asc, desc, func, or_, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from svc.apps.albums.models.album import Album
from svc.apps.products.models.category import Category
from svc.apps.products.models.product import Product, ProductStatus
from svc.apps.products.schemas.product import ProductCreate, ProductUpdate
from svc.core.repositories import BaseRepository
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo


class ProductRepository(BaseRepository[Product, ProductCreate, ProductUpdate]):
    """商品仓库类，提供商品数据访问方法
    
    该仓库类实现了Product模型的数据访问操作，
    包括商品的基本CRUD操作以及特定的数据查询和统计功能。
    """
    
    def __init__(self, db: AsyncSession):
        """初始化商品仓库"""
        super().__init__(db, Product)
    
    async def create_with_album(self, product_data: ProductCreate, album_id: int) -> Product:
        """创建商品并关联图册ID"""
        new_product = Product(**product_data.model_dump(), album_id=album_id)
        self.db.add(new_product)
        await self.db.flush()
        await self.db.refresh(new_product)
        return new_product

    async def get_products_by_category(self, category_id: int) -> List[Product]:
        """按分类ID查找所有商品"""
        stmt = select(Product).where(
            Product.category_id == category_id,
            Product.deleted_at.is_(None),
            Product.status == "active"
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_featured(self) -> List[Product]:
        """查找所有推荐商品"""
        stmt = select(Product).where(
            Product.is_featured == True,
            Product.deleted_at.is_(None),
            Product.status == "active"
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def increment_view_count(self, product_id: int) -> Optional[Product]:
        """增加商品浏览次数
        
        Args:
            product_id: 商品ID
            
        Returns:
            Optional[Product]: 更新后的商品对象，不存在则返回None
        """
        product = await self.get_by_id(product_id)
        if not product:
            return None
            
        update_data = {"view_count": product.view_count + 1}
        return await self.update(product, update_data)
    
    async def increment_sales_count(self, product_id: int, quantity: int = 1) -> Optional[Product]:
        """增加商品销售次数
        
        Args:
            product_id: 商品ID
            quantity: 销售数量
            
        Returns:
            Optional[Product]: 更新后的商品对象，不存在则返回None
        """
        product = await self.get_by_id(product_id)
        if not product:
            return None
            
        update_data = {"sales_count": product.sales_count + quantity}
        return await self.update(product, update_data)
    
    async def update_stock_quantity(
        self, 
        product_id: int, 
        quantity_change: int
    ) -> Optional[Product]:
        """更新商品库存数量
        
        Args:
            product_id: 商品ID
            quantity_change: 库存变化量（正数为增加，负数为减少）
            
        Returns:
            Optional[Product]: 更新后的商品对象，不存在则返回None
        """
        product = await self.get_by_id(product_id)
        if not product:
            return None
            
        new_quantity = max(0, product.stock_quantity + quantity_change)
        update_data = {"stock_quantity": new_quantity}
        return await self.update(product, update_data)

    async def get_by_id(self, id: int) -> Optional[Product]:
        query = select(self.model).where(self.model.id == id).options(
            selectinload(self.model.album).selectinload(Album.cover_image)
        )
        result = await self.db.execute(query)
        return result.scalars().first()


