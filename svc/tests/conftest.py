"""
测试配置文件
提供测试所需的fixtures和配置
"""
import asyncio
import os
from typing import AsyncGenerator, Generator

import pytest
import pytest_asyncio
from fastapi import FastAPI
from fastapi.testclient import TestClient
from httpx import AsyncClient
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from svc.core.config.settings import get_settings
from svc.core.database.session import get_db
from svc.core.models.base import Base
from svc.main import create_app


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def test_db_engine():
    """创建测试数据库引擎"""
    # 使用内存SQLite数据库进行测试
    DATABASE_URL = "sqlite+aiosqlite:///:memory:"
    
    engine = create_async_engine(
        DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
        echo=False
    )
    
    # 创建所有表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    # 清理
    await engine.dispose()


@pytest.fixture
async def test_db_session(test_db_engine) -> AsyncGenerator[AsyncSession, None]:
    """创建测试数据库会话"""
    async_session = sessionmaker(
        test_db_engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        yield session
        await session.rollback()


@pytest.fixture
def test_app(test_db_session) -> FastAPI:
    """创建测试应用"""
    app = create_app()
    
    # 覆盖数据库依赖
    async def override_get_db():
        yield test_db_session
    
    app.dependency_overrides[get_db] = override_get_db
    
    return app


@pytest.fixture
def test_client(test_app) -> TestClient:
    """创建测试客户端"""
    return TestClient(test_app)


@pytest.fixture
async def async_test_client(test_app) -> AsyncGenerator[AsyncClient, None]:
    """创建异步测试客户端"""
    async with AsyncClient(app=test_app, base_url="http://test") as client:
        yield client


@pytest.fixture
def auth_headers():
    """创建认证头部（模拟）"""
    # 这里应该根据实际的认证机制来生成token
    # 暂时返回空字典，实际使用时需要实现
    return {}


@pytest.fixture
def admin_headers():
    """创建管理员认证头部（模拟）"""
    # 这里应该根据实际的认证机制来生成admin token
    # 暂时返回空字典，实际使用时需要实现
    return {}


# 测试数据fixtures
@pytest.fixture
def sample_category_data():
    """示例分类数据"""
    return {
        "name": "测试分类",
        "description": "这是一个测试分类",
        "slug": "test-category",
        "status": "active",
        "is_featured": False,
        "sort_order": 1,
        "level": 1,
        "seo_title": "测试分类 - SEO标题",
        "seo_description": "测试分类的SEO描述",
        "seo_keywords": "测试,分类,关键词"
    }


@pytest.fixture
def sample_product_data():
    """示例商品数据"""
    return {
        "name": "测试商品",
        "description": "这是一个测试商品",
        "short_description": "测试商品简述",
        "sku": "TEST-PRODUCT-001",
        "barcode": "1234567890123",
        "price": 9999,  # 99.99元，以分为单位
        "cost_price": 5000,  # 50.00元
        "market_price": 12999,  # 129.99元
        "currency": "CNY",
        "status": "active",
        "is_featured": True,
        "is_digital": False,
        "track_inventory": True,
        "stock_quantity": 100,
        "min_stock_level": 10,
        "max_stock_level": 1000,
        "weight": 0.5,
        "dimensions": {"length": 10, "width": 5, "height": 2},
        "attributes": {"color": "red", "size": "M"},
        "seo_title": "测试商品 - SEO标题",
        "seo_description": "测试商品的SEO描述",
        "seo_keywords": "测试,商品,关键词"
    }


@pytest.fixture
def sample_spec_data():
    """示例规格数据"""
    return {
        "name": "颜色",
        "description": "商品颜色规格",
        "type": "select",
        "is_required": True,
        "sort_order": 1,
        "status": "active"
    }


@pytest.fixture
def sample_spec_option_data():
    """示例规格选项数据"""
    return {
        "value": "红色",
        "display_name": "红色",
        "sort_order": 1,
        "is_default": False,
        "status": "active"
    }


@pytest.fixture
def sample_sku_data():
    """示例SKU数据"""
    return {
        "sku_code": "TEST-SKU-001",
        "barcode": "SKU1234567890",
        "price": 9999,
        "cost_price": 5000,
        "stock_quantity": 50,
        "weight": 0.5,
        "dimensions": {"length": 10, "width": 5, "height": 2},
        "status": "active"
    }


@pytest.fixture
def sample_inventory_data():
    """示例库存数据"""
    return {
        "available_quantity": 100,
        "reserved_quantity": 0,
        "warehouse_location": "A-01-001",
        "batch_number": "BATCH-001",
        "supplier_id": 1,
        "cost_price": 5000,
        "status": "available"
    }
