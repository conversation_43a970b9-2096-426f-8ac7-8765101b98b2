from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from svc.core.repositories.base import BaseRepository
from svc.apps.products.models.sku import ProductSKU

class ProductSKURepository(BaseRepository[ProductSKU, None, None]):
    """
    SKU仓库，复用BaseRepository，暴露适合服务层调用的接口
    """
    def __init__(self, db: AsyncSession):
        super().__init__(db, ProductSKU)

    async def get_skus_by_product(self, product_id: int) -> List[ProductSKU]:
        """按产品ID查找所有SKU"""
        stmt = select(ProductSKU).where(
            ProductSKU.product_id == product_id,
            ProductSKU.deleted_at.is_(None),
            ProductSKU.status == "active"
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_skus_by_option(self, option_id: int) -> List[ProductSKU]:
        """查找某规格选项下所有SKU"""
        stmt = select(ProductSKU).join(ProductSKU.spec_options).where(
            ProductSKU.deleted_at.is_(None),
            ProductSKU.status == "active",
            ProductSKU.spec_options.any(id=option_id)
        )
        result = await self.db.execute(stmt)
        return result.scalars().all() 