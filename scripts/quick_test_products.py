#!/usr/bin/env python3
"""
产品模块API快速测试脚本
用于快速验证产品模块的主要API接口是否正常工作
"""
import asyncio
import json
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import httpx
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from svc.core.database.session import get_db
from svc.core.models.base import Base
# 导入应用和数据库相关
from svc.main import create_app


class QuickAPITester:
    """快速API测试器"""

    def __init__(self):
        # 设置测试环境变量
        os.environ["TESTING"] = "1"

        self.app = create_app()
        self.test_results = []
        self.test_engine = None
        self.client = None

    async def setup_test_db(self):
        """设置测试数据库"""
        # 使用内存SQLite数据库进行测试
        DATABASE_URL = "sqlite+aiosqlite:///:memory:"

        self.test_engine = create_async_engine(
            DATABASE_URL,
            connect_args={"check_same_thread": False},
            poolclass=StaticPool,
            echo=False
        )

        # 创建所有表
        async with self.test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        # 创建测试会话工厂
        async_session = sessionmaker(
            self.test_engine, class_=AsyncSession, expire_on_commit=False
        )

        # 覆盖数据库依赖
        async def override_get_db():
            async with async_session() as session:
                yield session

        self.app.dependency_overrides[get_db] = override_get_db

        # 创建测试客户端
        self.client = TestClient(self.app)

    async def cleanup_test_db(self):
        """清理测试数据库"""
        if self.test_engine:
            await self.test_engine.dispose()
    
    def test_endpoint(self, method: str, url: str, data: dict = None, expected_status: int = 200, description: str = ""):
        """测试单个API端点"""
        try:
            if method.upper() == "GET":
                response = self.client.get(url)
            elif method.upper() == "POST":
                response = self.client.post(url, json=data)
            elif method.upper() == "PUT":
                response = self.client.put(url, json=data)
            elif method.upper() == "DELETE":
                response = self.client.delete(url)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            success = response.status_code == expected_status
            
            result = {
                "method": method.upper(),
                "url": url,
                "expected_status": expected_status,
                "actual_status": response.status_code,
                "success": success,
                "description": description,
                "response_data": response.json() if response.content else None
            }
            
            self.test_results.append(result)
            
            status_icon = "✅" if success else "❌"
            print(f"{status_icon} {method.upper()} {url} - {description}")
            if not success:
                print(f"   期望状态码: {expected_status}, 实际状态码: {response.status_code}")
                if response.content:
                    print(f"   响应内容: {response.text[:200]}...")
            
            return result
            
        except Exception as e:
            result = {
                "method": method.upper(),
                "url": url,
                "expected_status": expected_status,
                "actual_status": None,
                "success": False,
                "description": description,
                "error": str(e)
            }
            
            self.test_results.append(result)
            print(f"❌ {method.upper()} {url} - {description}")
            print(f"   错误: {str(e)}")
            
            return result
    
    def run_basic_tests(self):
        """运行基础API测试"""
        if not self.client:
            print("❌ 测试客户端未初始化")
            return

        print("🚀 开始运行产品模块基础API测试...")
        print("=" * 60)
        
        # 测试商品API
        print("\n📦 测试商品API")
        print("-" * 30)

        # 使用正确的API路径
        self.test_endpoint("GET", "/api/v1/product/list", expected_status=200, description="获取商品列表(客户端)")
        self.test_endpoint("GET", "/api/v1/product/recommended", expected_status=200, description="获取推荐商品")
        self.test_endpoint("GET", "/api/v1/product/1", expected_status=404, description="获取不存在的商品详情")

        # 测试分类API
        print("\n📁 测试分类API")
        print("-" * 30)

        self.test_endpoint("GET", "/api/v1/category/list", expected_status=200, description="获取分类列表(客户端)")
        self.test_endpoint("GET", "/api/v1/category/tree", expected_status=200, description="获取分类树")

        # 测试规格API
        print("\n📏 测试规格API")
        print("-" * 30)

        self.test_endpoint("GET", "/api/v1/spec/specs", expected_status=200, description="获取规格列表(客户端)")
        self.test_endpoint("GET", "/api/v1/spec/specs/1/options", expected_status=200, description="获取规格选项")

        # 测试SKU API
        print("\n🏷️ 测试SKU API")
        print("-" * 30)

        self.test_endpoint("GET", "/api/v1/sku/products/1/skus", expected_status=200, description="获取商品SKU列表")
        self.test_endpoint("GET", "/api/v1/sku/skus/1", expected_status=404, description="获取不存在的SKU详情")

        # 测试库存API (内部API)
        print("\n📊 测试库存API")
        print("-" * 30)

        self.test_endpoint("GET", "/api/v1/inventory/internal/product/1", expected_status=200, description="获取商品库存(内部)")

        # 管理端API测试 (预期会因为认证失败)
        print("\n🔐 测试管理端API (预期认证失败)")
        print("-" * 40)

        self.test_endpoint("GET", "/api/v1/product/admin/list", expected_status=401, description="管理端商品列表")
        self.test_endpoint("GET", "/api/v1/category/admin/list", expected_status=401, description="管理端分类列表")
        self.test_endpoint("GET", "/api/v1/spec/admin/specs", expected_status=401, description="管理端规格列表")
        self.test_endpoint("GET", "/api/v1/sku/admin/skus", expected_status=401, description="管理端SKU列表")
        self.test_endpoint("GET", "/api/v1/inventory/admin/list", expected_status=401, description="管理端库存列表")
    
    def print_summary(self):
        """打印测试结果汇总"""
        print("\n" + "=" * 60)
        print("📊 测试结果汇总")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ 失败的测试 ({failed_tests}个):")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['method']} {result['url']} - {result['description']}")
                    if "error" in result:
                        print(f"    错误: {result['error']}")
                    else:
                        print(f"    状态码: {result['actual_status']} (期望: {result['expected_status']})")
        
        print("\n💡 提示:")
        print("- 401错误是正常的，表示需要认证")
        print("- 404错误是正常的，表示资源不存在")
        print("- 200错误表示API端点可以正常访问")
        
        print("\n" + "=" * 60)
    
    def test_health_check(self):
        """测试健康检查端点"""
        if not self.client:
            print("❌ 测试客户端未初始化")
            return

        print("🏥 测试应用健康状态...")

        # 测试根路径
        try:
            response = self.client.get("/")
            if response.status_code == 200:
                print("✅ 应用根路径正常")
            else:
                print(f"⚠️ 应用根路径返回状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ 应用根路径测试失败: {e}")

        # 测试健康检查端点（如果存在）
        try:
            response = self.client.get("/health")
            if response.status_code == 200:
                print("✅ 健康检查端点正常")
            elif response.status_code == 404:
                print("ℹ️ 健康检查端点不存在")
            else:
                print(f"⚠️ 健康检查端点返回状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ 健康检查端点测试失败: {e}")


async def main():
    """主函数"""
    print("🧪 产品模块API快速测试工具")
    print("用于快速验证产品模块的主要API接口")
    print()

    tester = QuickAPITester()

    try:
        # 设置测试数据库
        await tester.setup_test_db()

        # 测试应用健康状态
        tester.test_health_check()

        # 运行基础API测试
        tester.run_basic_tests()

        # 打印汇总结果
        tester.print_summary()

        # 根据结果设置退出码
        failed_count = sum(1 for r in tester.test_results if not r["success"])
        if failed_count > 0:
            print(f"\n⚠️ 有 {failed_count} 个测试失败，请检查相关API实现")
            return 1
        else:
            print("\n🎉 所有测试都通过了！")
            return 0

    finally:
        # 清理测试数据库
        await tester.cleanup_test_db()


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
