from svc.core.models.result import Result, ResultFactory
from svc.core.exceptions.error_codes import <PERSON>rrorCode
from typing import Any, Optional, Type, TypeVar, cast

ResultType = TypeVar("ResultType", bound=Result)

class ErrorResultMixin(object):
    result_factory: type = ResultFactory
    resource_type: str = "资源"

    def create_error_result(self, error_code: int, error_message: str, data: Optional[Any] = None) -> ResultType:
        self.logger.debug(f"创建错误结果: error_code={error_code}, error_message={error_message}")
        return cast(ResultType, self.result_factory.error(error_code, error_message, data))

    def create_success_result(self, data: Optional[Any] = None) -> ResultType:
        return cast(ResultType, self.result_factory.success(data))

    def resource_not_found_result(self, resource_id, result_code: int = ErrorCode.NOT_FOUND) -> ResultType:
        self.logger.debug(f"{self.resource_type}不存在: ID={resource_id}")
        return cast(ResultType, self.result_factory.resource_not_found(
            resource_type=self.resource_type,
            resource_id=resource_id,
            result_code=result_code
        ))

    def permission_denied_result(self, resource_id=None, result_code: int = ErrorCode.PERMISSION_DENIED) -> ResultType:
        self.logger.debug(f"权限不足: 资源类型={self.resource_type}, ID={resource_id}")
        return cast(ResultType, self.result_factory.permission_denied(
            resource_type=self.resource_type,
            resource_id=resource_id,
            result_code=result_code
        )) 