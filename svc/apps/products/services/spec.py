"""
商品规格服务层
提供规格和规格选项的业务逻辑处理
"""
from typing import Any, Dict, List, Optional

from fastapi_events.dispatcher import dispatch
from redis.asyncio import Redis

from svc.apps.products.models.spec import Spec, SpecOption
from svc.apps.products.repositories.spec import (SpecOptionRepository,
                                                 SpecRepository)
from svc.apps.products.schemas.spec import (SpecCreate, SpecListResponse,
                                            SpecOptionCreate,
                                            SpecOptionListResponse,
                                            SpecOptionResponse,
                                            SpecOptionUpdate, SpecResponse,
                                            SpecUpdate)
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.models.result import Result
from svc.core.services.base import BaseService
from svc.core.services.mixins.batch_operation import BatchOperationMixin
from svc.core.services.mixins.cache import CacheMixin
from svc.core.services.mixins.error_result import ErrorResultMixin


class SpecService(BaseService, ErrorResultMixin, BatchOperationMixin, CacheMixin):
    """规格服务类，提供规格的创建、查询和管理功能
    
    该服务类负责：
    1. 规格的创建和管理
    2. 规格状态更新
    3. 规格与分类的关联管理
    4. 规格的缓存管理
    
    服务类依赖SpecRepository进行数据访问，
    实现业务逻辑与数据访问的分离。
    """
    
    # 设置资源类型名称
    resource_type = "spec"
    
    def __init__(
        self, 
        redis: Optional[Redis] = None, 
        spec_repo: Optional[SpecRepository] = None
    ):
        """初始化规格服务
        
        Args:
            redis: Redis客户端，用于缓存和分布式锁
            spec_repo: 规格仓库实例，不提供则创建新实例
        """
        BaseService.__init__(self, redis)
        self.spec_repo = spec_repo
        
    # === 缓存相关方法 ===
    
    async def _cache_spec(self, spec_id: int, spec_data: SpecResponse) -> None:
        """缓存规格数据"""
        if self.redis:
            cache_key = f"spec:{spec_id}"
            await self.redis.setex(
                cache_key, 
                3600,  # 1小时缓存
                spec_data.model_dump_json()
            )
    
    async def _get_cached_spec(self, spec_id: int) -> Optional[SpecResponse]:
        """从缓存获取规格数据"""
        if not self.redis:
            return None
            
        cache_key = f"spec:{spec_id}"
        cached_data = await self.redis.get(cache_key)
        if cached_data:
            return SpecResponse.model_validate_json(cached_data)
        return None
    
    async def _invalidate_spec_cache(self, spec_id: int) -> None:
        """清除规格缓存"""
        if self.redis:
            cache_key = f"spec:{spec_id}"
            await self.redis.delete(cache_key)
    
    async def _invalidate_category_spec_cache(self, category_id: int) -> None:
        """清除分类相关的规格缓存"""
        if self.redis:
            pattern = f"category:{category_id}:specs:*"
            keys = await self.redis.keys(pattern)
            if keys:
                await self.redis.delete(*keys)
    
    # === 核心业务方法 ===
    
    async def create_spec(self, params: SpecCreate) -> Result[SpecResponse]:
        """创建规格"""
        try:
            self.logger.info(f"创建规格: name={params.name}")
            
            # 检查规格名称是否已存在
            existing_spec = await self.spec_repo.get_one(name=params.name)
            if existing_spec:
                self.logger.warning(f"规格名称已存在: {params.name}")
                return self.create_error_result(
                    error_code=ErrorCode.ALREADY_EXISTS,
                    error_message=f"规格名称 '{params.name}' 已存在"
                )
            
            # 创建规格
            spec_data = params.model_dump()
            spec = await self.spec_repo.create(spec_data)
            
            # 构建响应
            spec_response = SpecResponse.model_validate(spec.to_dict())
            
            # 缓存规格
            await self._cache_spec(spec.id, spec_response)
            
            # 触发规格创建事件
            event_data = spec_response.model_dump()
            dispatch("products:spec:created", payload=event_data)
            
            self.logger.info(f"规格创建成功: id={spec.id}, name={spec.name}")
            return self.create_success_result(spec_response, status_code=201)
            
        except Exception as e:
            self.logger.error(f"创建规格失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"创建规格失败: {str(e)}"
            )
    
    async def get_spec(self, spec_id: int, user_mode: bool = False) -> Result[SpecResponse]:
        """获取规格详情"""
        try:
            self.logger.info(f"获取规格详情: id={spec_id}")
            
            # 先尝试从缓存获取
            cached_spec = await self._get_cached_spec(spec_id)
            if cached_spec:
                self.logger.debug(f"从缓存获取到规格: id={spec_id}")
                return self.create_success_result(cached_spec)
            
            # 从数据库获取
            spec = await self.spec_repo.get_by_id(spec_id)
            if not spec:
                self.logger.warning(f"规格不存在: id={spec_id}")
                return self.resource_not_found_result(spec_id)
            
            # 构建响应并缓存
            spec_response = SpecResponse.model_validate(spec.to_dict())
            await self._cache_spec(spec.id, spec_response)
            
            self.logger.info(f"获取规格详情成功: id={spec_id}")
            return self.create_success_result(spec_response)
            
        except Exception as e:
            self.logger.error(f"获取规格详情失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取规格详情失败: {str(e)}"
            )
    
    async def update_spec(self, spec_id: int, params: SpecUpdate) -> Result[SpecResponse]:
        """更新规格"""
        try:
            self.logger.info(f"更新规格: id={spec_id}")
            
            # 检查规格是否存在
            spec = await self.spec_repo.get_by_id(spec_id)
            if not spec:
                self.logger.warning(f"规格不存在: id={spec_id}")
                return self.resource_not_found_result(spec_id)
            
            # 如果更新名称，检查是否重复
            if params.name and params.name != spec.name:
                existing_spec = await self.spec_repo.get_one(name=params.name)
                if existing_spec:
                    return self.create_error_result(
                        error_code=ErrorCode.ALREADY_EXISTS,
                        error_message=f"规格名称 '{params.name}' 已存在"
                    )
            
            # 更新规格
            update_data = {k: v for k, v in params.model_dump().items() if v is not None}
            updated_spec = await self.spec_repo.update(spec, update_data)
            
            # 构建响应
            spec_response = SpecResponse.model_validate(updated_spec.to_dict())
            
            # 更新缓存
            await self._cache_spec(spec_id, spec_response)
            
            # 触发规格更新事件
            event_data = spec_response.model_dump()
            dispatch("products:spec:updated", payload=event_data)
            
            self.logger.info(f"规格更新成功: id={spec_id}")
            return self.create_success_result(spec_response)
            
        except Exception as e:
            self.logger.error(f"更新规格失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"更新规格失败: {str(e)}"
            )
    
    async def delete_spec(self, spec_id: int) -> Result[Dict[str, Any]]:
        """删除规格（软删除）"""
        try:
            self.logger.info(f"删除规格: id={spec_id}")
            
            # 检查规格是否存在
            spec = await self.spec_repo.get_by_id(spec_id)
            if not spec:
                self.logger.warning(f"规格不存在: id={spec_id}")
                return self.resource_not_found_result(spec_id)
            
            # 软删除规格
            await self.spec_repo.soft_delete(spec)
            
            # 清除缓存
            await self._invalidate_spec_cache(spec_id)
            
            # 触发规格删除事件
            event_data = {"spec_id": spec_id}
            dispatch("products:spec:deleted", payload=event_data)
            
            self.logger.info(f"规格删除成功: id={spec_id}")
            return self.create_success_result({"message": "规格删除成功"})
            
        except Exception as e:
            self.logger.error(f"删除规格失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"删除规格失败: {str(e)}"
            )
    
    async def get_specs_list(
        self,
        page_num: int = 1,
        page_size: int = 20,
        status: Optional[str] = None,
        search_term: Optional[str] = None,
        category_id: Optional[int] = None
    ) -> Result[SpecListResponse]:
        """获取规格列表"""
        try:
            self.logger.info(f"获取规格列表: page={page_num}, size={page_size}")

            # 构建查询条件
            filters = {}
            if status:
                filters["status"] = status
            if search_term:
                filters["name__icontains"] = search_term

            # 根据分类获取规格（特殊情况，需要使用专门的方法）
            if category_id:
                # 使用专门的方法获取分类下的规格，然后手动分页
                all_specs = await self.spec_repo.get_specs_by_category(category_id)
                total = len(all_specs)

                # 手动分页
                start = (page_num - 1) * page_size
                end = start + page_size
                specs = all_specs[start:end]
            else:
                # 使用统一的分页方法
                specs, total = await self.spec_repo.get_paginated(
                    page_num=page_num,
                    page_size=page_size,
                    order_by="sort_order",
                    order_direction="asc",
                    **filters
                )

            # 构建响应
            spec_responses = [SpecResponse.model_validate(spec.to_dict()) for spec in specs]

            # 计算总页数
            page_count = (total + page_size - 1) // page_size if page_size > 0 else 0

            # 构建分页响应
            response = SpecListResponse(
                items=spec_responses,
                total=total,
                page_num=page_num,
                page_size=page_size,
                page_count=page_count
            )

            self.logger.info(f"获取规格列表成功: count={len(specs)}, total={total}")
            return self.create_success_result(response)

        except Exception as e:
            self.logger.error(f"获取规格列表失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取规格列表失败: {str(e)}"
            )


class SpecOptionService(BaseService, ErrorResultMixin, BatchOperationMixin, CacheMixin):
    """规格选项服务类，提供规格选项的创建、查询和管理功能

    该服务类负责：
    1. 规格选项的创建和管理
    2. 规格选项状态更新
    3. 规格选项与SKU的关联管理
    4. 规格选项的缓存管理

    服务类依赖SpecOptionRepository进行数据访问，
    实现业务逻辑与数据访问的分离。
    """

    # 设置资源类型名称
    resource_type = "spec_option"

    def __init__(
        self,
        redis: Optional[Redis] = None,
        spec_option_repo: Optional[SpecOptionRepository] = None,
        spec_repo: Optional[SpecRepository] = None
    ):
        """初始化规格选项服务

        Args:
            redis: Redis客户端，用于缓存和分布式锁
            spec_option_repo: 规格选项仓库实例，不提供则创建新实例
            spec_repo: 规格仓库实例，用于验证规格是否存在
        """
        BaseService.__init__(self, redis)
        self.spec_option_repo = spec_option_repo
        self.spec_repo = spec_repo

    # === 缓存相关方法 ===

    async def _cache_spec_option(self, option_id: int, option_data: SpecOptionResponse) -> None:
        """缓存规格选项数据"""
        if self.redis:
            cache_key = f"spec_option:{option_id}"
            await self.redis.setex(
                cache_key,
                3600,  # 1小时缓存
                option_data.model_dump_json()
            )

    async def _get_cached_spec_option(self, option_id: int) -> Optional[SpecOptionResponse]:
        """从缓存获取规格选项数据"""
        if not self.redis:
            return None

        cache_key = f"spec_option:{option_id}"
        cached_data = await self.redis.get(cache_key)
        if cached_data:
            return SpecOptionResponse.model_validate_json(cached_data)
        return None

    async def _invalidate_spec_option_cache(self, option_id: int) -> None:
        """清除规格选项缓存"""
        if self.redis:
            cache_key = f"spec_option:{option_id}"
            await self.redis.delete(cache_key)

    async def _invalidate_spec_options_cache(self, spec_id: int) -> None:
        """清除规格相关的选项缓存"""
        if self.redis:
            pattern = f"spec:{spec_id}:options:*"
            keys = await self.redis.keys(pattern)
            if keys:
                await self.redis.delete(*keys)

    # === 核心业务方法 ===

    async def create_spec_option(self, params: SpecOptionCreate) -> Result[SpecOptionResponse]:
        """创建规格选项"""
        try:
            self.logger.info(f"创建规格选项: spec_id={params.spec_id}, value={params.value}")

            # 验证规格是否存在
            if self.spec_repo:
                spec = await self.spec_repo.get_by_id(params.spec_id)
                if not spec:
                    return self.create_error_result(
                        error_code=ErrorCode.NOT_FOUND,
                        error_message=f"规格 {params.spec_id} 不存在"
                    )

            # 检查规格选项值是否已存在
            if await self.spec_option_repo.check_option_exists(params.spec_id, params.value):
                self.logger.warning(f"规格选项值已存在: spec_id={params.spec_id}, value={params.value}")
                return self.create_error_result(
                    error_code=ErrorCode.ALREADY_EXISTS,
                    error_message=f"规格选项值 '{params.value}' 已存在"
                )

            # 创建规格选项
            option_data = params.model_dump()
            option = await self.spec_option_repo.create(option_data)

            # 构建响应
            option_response = SpecOptionResponse.model_validate(option.to_dict())

            # 缓存规格选项
            await self._cache_spec_option(option.id, option_response)

            # 清除相关缓存
            await self._invalidate_spec_options_cache(params.spec_id)

            # 触发规格选项创建事件
            event_data = option_response.model_dump()
            dispatch("products:spec_option:created", payload=event_data)

            self.logger.info(f"规格选项创建成功: id={option.id}, value={option.value}")
            return self.create_success_result(option_response, status_code=201)

        except Exception as e:
            self.logger.error(f"创建规格选项失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"创建规格选项失败: {str(e)}"
            )

    async def get_spec_option(self, option_id: int, user_mode: bool = False) -> Result[SpecOptionResponse]:
        """获取规格选项详情"""
        try:
            self.logger.info(f"获取规格选项详情: id={option_id}")

            # 先尝试从缓存获取
            cached_option = await self._get_cached_spec_option(option_id)
            if cached_option:
                self.logger.debug(f"从缓存获取到规格选项: id={option_id}")
                return self.create_success_result(cached_option)

            # 从数据库获取
            option = await self.spec_option_repo.get_by_id(option_id)
            if not option:
                self.logger.warning(f"规格选项不存在: id={option_id}")
                return self.resource_not_found_result(option_id)

            # 构建响应并缓存
            option_response = SpecOptionResponse.model_validate(option.to_dict())
            await self._cache_spec_option(option.id, option_response)

            self.logger.info(f"获取规格选项详情成功: id={option_id}")
            return self.create_success_result(option_response)

        except Exception as e:
            self.logger.error(f"获取规格选项详情失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取规格选项详情失败: {str(e)}"
            )

    async def update_spec_option(self, option_id: int, params: SpecOptionUpdate) -> Result[SpecOptionResponse]:
        """更新规格选项"""
        try:
            self.logger.info(f"更新规格选项: id={option_id}")

            # 检查规格选项是否存在
            option = await self.spec_option_repo.get_by_id(option_id)
            if not option:
                self.logger.warning(f"规格选项不存在: id={option_id}")
                return self.resource_not_found_result(option_id)

            # 如果更新值，检查是否重复
            if params.value and params.value != option.value:
                if await self.spec_option_repo.check_option_exists(option.spec_id, params.value, option_id):
                    return self.create_error_result(
                        error_code=ErrorCode.ALREADY_EXISTS,
                        error_message=f"规格选项值 '{params.value}' 已存在"
                    )

            # 更新规格选项
            update_data = {k: v for k, v in params.model_dump().items() if v is not None}
            updated_option = await self.spec_option_repo.update(option, update_data)

            # 构建响应
            option_response = SpecOptionResponse.model_validate(updated_option.to_dict())

            # 更新缓存
            await self._cache_spec_option(option_id, option_response)

            # 清除相关缓存
            await self._invalidate_spec_options_cache(updated_option.spec_id)

            # 触发规格选项更新事件
            event_data = option_response.model_dump()
            dispatch("products:spec_option:updated", payload=event_data)

            self.logger.info(f"规格选项更新成功: id={option_id}")
            return self.create_success_result(option_response)

        except Exception as e:
            self.logger.error(f"更新规格选项失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"更新规格选项失败: {str(e)}"
            )

    async def delete_spec_option(self, option_id: int) -> Result[Dict[str, Any]]:
        """删除规格选项（软删除）"""
        try:
            self.logger.info(f"删除规格选项: id={option_id}")

            # 检查规格选项是否存在
            option = await self.spec_option_repo.get_by_id(option_id)
            if not option:
                self.logger.warning(f"规格选项不存在: id={option_id}")
                return self.resource_not_found_result(option_id)

            # 软删除规格选项
            await self.spec_option_repo.soft_delete(option)

            # 清除缓存
            await self._invalidate_spec_option_cache(option_id)
            await self._invalidate_spec_options_cache(option.spec_id)

            # 触发规格选项删除事件
            event_data = {"option_id": option_id, "spec_id": option.spec_id}
            dispatch("products:spec_option:deleted", payload=event_data)

            self.logger.info(f"规格选项删除成功: id={option_id}")
            return self.create_success_result({"message": "规格选项删除成功"})

        except Exception as e:
            self.logger.error(f"删除规格选项失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"删除规格选项失败: {str(e)}"
            )

    async def get_options_by_spec(
        self,
        spec_id: int,
        page_num: int = 1,
        page_size: int = 100
    ) -> Result[SpecOptionListResponse]:
        """获取规格的所有选项"""
        try:
            self.logger.info(f"获取规格选项列表: spec_id={spec_id}, page={page_num}, size={page_size}")

            # 使用分页方法获取规格选项
            options, total = await self.spec_option_repo.get_paginated(
                page_num=page_num,
                page_size=page_size,
                order_by="sort_order",
                order_direction="asc",
                spec_id=spec_id
            )

            # 构建响应
            option_responses = [SpecOptionResponse.model_validate(option.to_dict()) for option in options]

            # 计算总页数
            page_count = (total + page_size - 1) // page_size if page_size > 0 else 0

            # 构建分页响应
            response = SpecOptionListResponse(
                items=option_responses,
                total=total,
                page_num=page_num,
                page_size=page_size,
                page_count=page_count
            )

            self.logger.info(f"获取规格选项列表成功: spec_id={spec_id}, count={len(options)}, total={total}")
            return self.create_success_result(response)

        except Exception as e:
            self.logger.error(f"获取规格选项列表失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取规格选项列表失败: {str(e)}"
            )

    async def batch_create_spec_options(self, options_data: List[SpecOptionCreate]) -> Result[List[SpecOptionResponse]]:
        """批量创建规格选项"""
        try:
            self.logger.info(f"批量创建规格选项: count={len(options_data)}")

            created_options = []
            errors = []

            for i, option_data in enumerate(options_data):
                try:
                    result = await self.create_spec_option(option_data)
                    if result.success:
                        created_options.append(result.data)
                    else:
                        errors.append(f"第{i+1}个规格选项创建失败: {result.message}")
                except Exception as e:
                    errors.append(f"第{i+1}个规格选项创建异常: {str(e)}")

            if errors:
                self.logger.warning(f"批量创建规格选项部分失败: {errors}")
                return self.create_error_result(
                    error_code=ErrorCode.PARTIAL_SUCCESS,
                    error_message=f"部分规格选项创建失败: {'; '.join(errors)}",
                    data={"created": created_options, "errors": errors}
                )

            self.logger.info(f"批量创建规格选项成功: count={len(created_options)}")
            return self.create_success_result(created_options, status_code=201)

        except Exception as e:
            self.logger.error(f"批量创建规格选项失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"批量创建规格选项失败: {str(e)}"
            )
