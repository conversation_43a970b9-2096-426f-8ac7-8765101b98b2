from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from svc.core.repositories.base import BaseRepository
from svc.apps.products.models.spec import Spec, SpecOption

class SpecRepository(BaseRepository[Spec, None, None]):
    """
    规格仓库，复用BaseRepository，暴露适合服务层调用的接口
    """
    def __init__(self, db: AsyncSession):
        super().__init__(db, Spec)

    async def get_specs_by_category(self, category_id: int) -> List[Spec]:
        """按分类ID查找所有规格"""
        stmt = select(Spec).join(Spec.categories).where(
            Spec.deleted_at.is_(None),
            Spec.status == "active",
            Spec.categories.any(id=category_id)
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_options_by_spec(self, spec_id: int) -> List[SpecOption]:
        """查找某规格下所有选项"""
        stmt = select(SpecOption).where(
            SpecOption.spec_id == spec_id,
            SpecOption.deleted_at.is_(None),
            SpecOption.status == "active"
        )
        result = await self.db.execute(stmt)
        return result.scalars().all() 