from typing import Optional, List
from redis.asyncio import Redis
from fastapi_events.dispatcher import dispatch

from svc.apps.albums.models.album import Album
from svc.apps.albums.repositories.album import AlbumRepository
from svc.apps.albums.schemas.album import (
    AlbumCreate, AlbumUpdate, AlbumResponse, AlbumListResponse, GetAlbumsParams
)
from svc.core.services.base import BaseService
from svc.core.services.error_result_mixin import ErrorResultMixin
from svc.core.services.batch_operation_mixin import BatchOperationMixin
from svc.core.services.cache_mixin import CacheMixin
from svc.core.models.result import Result
from svc.core.exceptions.error_codes import ErrorCode

# 缓存配置
CACHE_TTL = 3600

class AlbumService(BaseService, ErrorResultMixin, BatchOperationMixin, CacheMixin):
    """图册服务类，提供图册的创建、查询和管理功能"""
    resource_type = "album"

    def __init__(self, redis: Optional[Redis] = None, album_repo: Optional[AlbumRepository] = None):
        super().__init__(redis)
        self.album_repo = album_repo

    async def get_resource_by_id(self, album_id: int) -> Optional[Album]:
        return await self.album_repo.get_by_id(album_id)

    async def get_album(self, album_id: int) -> Result[AlbumResponse]:
        try:
            album = await self.get_resource_by_id(album_id)
            if not album:
                return self.resource_not_found_result(album_id)
            response = AlbumResponse.model_validate(album.to_dict())
            return self.create_success_result(response)
        except Exception as e:
            return self.create_error_result(
                error_code=ErrorCode.NOT_FOUND,
                error_message=f"获取图册失败: {str(e)}"
            )

    async def get_albums(self, params: GetAlbumsParams) -> Result[AlbumListResponse]:
        try:
            page_num = params.page_num
            page_size = params.page_size
            skip = (page_num - 1) * page_size
            albums, total = await self.album_repo.get_albums(
                skip=skip,
                limit=page_size,
                status=params.status,
                tags=params.tags,
                search_term=params.search_term,
                order_by=params.order_by,
                order_desc=params.order_desc
            )
            responses = [AlbumResponse.model_validate(a.to_dict()) for a in albums]
            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0
            paginated = AlbumListResponse(
                items=responses,
                total=total,
                page_num=page_num,
                page_size=page_size,
                page_count=total_pages
            )
            return self.create_success_result(paginated)
        except Exception as e:
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取图册列表失败: {str(e)}"
            )

    async def create_album(self, params: AlbumCreate) -> Result[AlbumResponse]:
        try:
            album = await self.album_repo.create(params)
            response = AlbumResponse.model_validate(album.to_dict())
            dispatch("album:created", payload=response.model_dump())
            return self.create_success_result(response)
        except Exception as e:
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"创建图册失败: {str(e)}"
            )

    async def update_album(self, album_id: int, params: AlbumUpdate) -> Result[AlbumResponse]:
        try:
            album = await self.get_resource_by_id(album_id)
            if not album:
                return self.resource_not_found_result(album_id)
            update_data = params.model_dump(exclude_unset=True)
            album = await self.album_repo.update(album, data=update_data)
            response = AlbumResponse.model_validate(album.to_dict())
            dispatch("album:updated", payload=response.model_dump())
            return self.create_success_result(response)
        except Exception as e:
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"更新图册失败: {str(e)}"
            )

    async def delete_album(self, album_id: int) -> Result:
        try:
            album = await self.get_resource_by_id(album_id)
            if not album:
                return self.resource_not_found_result(album_id)
            await self.album_repo.delete(album)
            dispatch("album:deleted", payload={"id": album_id})
            return self.create_success_result({"id": album_id})
        except Exception as e:
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"删除图册失败: {str(e)}"
            ) 